/**
 * @file dpi_manager.cpp
 * @brief DPI管理器实现
 * <AUTHOR> Team
 * @version 2.0.0
 * @date 2025-01-30
 */

#include "pch.h"
#include "dpi_manager.h"
#include "engine.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

namespace HHBUI {

// ==================== UIDPIManager实现 ====================

UIDPIManager::UIDPIManager()
    : nextCallbackId_(1)
    , initialized_(false)
    , dpiAwarenessSet_(false)
    , getDpiForMonitor_(nullptr)
    , getDpiForWindow_(nullptr)
    , setProcessDpiAwarenessContext_(nullptr)
    , shcoreModule_(nullptr)
    , user32Module_(nullptr)
    , lastMonitorUpdate_(std::chrono::steady_clock::now()) {
}

UIDPIManager::~UIDPIManager() {
    Shutdown();
}

VoidResult UIDPIManager::Initialize(const DPIConfig& config) {
    try {
        if (initialized_.load()) {
            return VoidResult(E_FAIL, L"DPI管理器已经初始化");
        }
        
        // 保存配置
        {
            std::lock_guard<std::mutex> lock(configMutex_);
            config_ = config;
        }
        
        // 加载系统API
        shcoreModule_ = LoadLibraryW(L"Shcore.dll");
        if (shcoreModule_) {
            getDpiForMonitor_ = reinterpret_cast<GetDpiForMonitorProc>(
                GetProcAddress(shcoreModule_, "GetDpiForMonitor"));
        }
        
        user32Module_ = LoadLibraryW(L"User32.dll");
        if (user32Module_) {
            getDpiForWindow_ = reinterpret_cast<GetDpiForWindowProc>(
                GetProcAddress(user32Module_, "GetDpiForWindow"));
            setProcessDpiAwarenessContext_ = reinterpret_cast<SetProcessDpiAwarenessContextProc>(
                GetProcAddress(user32Module_, "SetProcessDpiAwarenessContext"));
        }
        
        // 初始化DPI感知
        auto awarenessResult = InitializeDPIAwareness();
        if (awarenessResult.IsFailure()) {
            HHBUI_LOG_WARNING(L"DPI感知初始化失败: " + awarenessResult.GetErrorMessage());
        }
        
        // 枚举监视器
        EnumerateMonitors();
        
        initialized_.store(true);
        HHBUI_LOG_INFO(L"DPI管理器初始化成功");
        
        return VoidResult();
        
    } catch (const std::exception& e) {
        std::wstring message = L"DPI管理器初始化异常: ";
        int size = MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, nullptr, 0);
        if (size > 0) {
            std::wstring wstr(size - 1, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, &wstr[0], size);
            message += wstr;
        }
        return VoidResult(E_UNEXPECTED, message);
    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"DPI管理器初始化未知异常");
    }
}

void UIDPIManager::Shutdown() {
    try {
        if (!initialized_.load()) {
            return;
        }
        
        // 清理回调
        {
            std::lock_guard<std::mutex> lock(callbackMutex_);
            dpiChangeCallbacks_.clear();
            monitorChangeCallbacks_.clear();
        }
        
        // 清理缓存
        ClearCache();
        
        // 清理监视器信息
        {
            std::lock_guard<std::mutex> lock(monitorsMutex_);
            monitors_.clear();
        }
        
        // 释放系统模块
        if (shcoreModule_) {
            FreeLibrary(shcoreModule_);
            shcoreModule_ = nullptr;
            getDpiForMonitor_ = nullptr;
        }
        
        if (user32Module_) {
            FreeLibrary(user32Module_);
            user32Module_ = nullptr;
            getDpiForWindow_ = nullptr;
            setProcessDpiAwarenessContext_ = nullptr;
        }
        
        initialized_.store(false);
        HHBUI_LOG_INFO(L"DPI管理器已关闭");
        
    } catch (...) {
        HHBUI_LOG_WARNING(L"DPI管理器关闭时发生异常");
    }
}

VoidResult UIDPIManager::UpdateConfig(const DPIConfig& config) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"DPI管理器未初始化");
        }
        
        {
            std::lock_guard<std::mutex> lock(configMutex_);
            config_ = config;
        }
        
        // 清理缓存以应用新配置
        ClearCache();
        
        HHBUI_LOG_DEBUG(L"DPI配置已更新");
        return VoidResult();
        
    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"DPI配置更新时发生异常");
    }
}

DPIInfo UIDPIManager::GetSystemDPI() const {
    try {
        HDC hdc = GetDC(nullptr);
        if (!hdc) {
            return DPIInfo();
        }
        
        uint32_t dpiX = static_cast<uint32_t>(GetDeviceCaps(hdc, LOGPIXELSX));
        uint32_t dpiY = static_cast<uint32_t>(GetDeviceCaps(hdc, LOGPIXELSY));
        ReleaseDC(nullptr, hdc);
        
        DPIInfo info(dpiX, dpiY);
        
        // 应用自定义缩放
        {
            std::lock_guard<std::mutex> lock(configMutex_);
            if (config_.customScale > 0.0f) {
                info.scaleX = config_.customScale;
                info.scaleY = config_.customScale;
            }
            
            // 应用缩放限制
            info.scaleX = std::clamp(info.scaleX, config_.minScale, config_.maxScale);
            info.scaleY = std::clamp(info.scaleY, config_.minScale, config_.maxScale);
            
            // 量化缩放值
            info.scaleX = QuantizeScale(info.scaleX);
            info.scaleY = QuantizeScale(info.scaleY);
        }
        
        return info;
        
    } catch (...) {
        return DPIInfo();
    }
}

DPIInfo UIDPIManager::GetWindowDPI(HWND window) const {
    try {
        if (!window) {
            return GetSystemDPI();
        }
        
        // 尝试使用Windows 10的API
        if (getDpiForWindow_) {
            UINT dpi = getDpiForWindow_(window);
            if (dpi > 0) {
                return DPIInfo(dpi, dpi);
            }
        }
        
        // 回退到监视器DPI
        HMONITOR monitor = MonitorFromWindow(window, MONITOR_DEFAULTTONEAREST);
        return GetMonitorDPI(monitor);
        
    } catch (...) {
        return GetSystemDPI();
    }
}

DPIInfo UIDPIManager::GetMonitorDPI(HMONITOR monitor) const {
    try {
        if (!monitor) {
            return GetSystemDPI();
        }
        
        // 检查缓存
        {
            std::lock_guard<std::mutex> lock(dpiCacheMutex_);
            auto it = dpiCache_.find(monitor);
            if (it != dpiCache_.end()) {
                return it->second;
            }
        }
        
        // 获取监视器DPI
        DPIInfo info = GetMonitorDPIInternal(monitor);
        
        // 缓存结果
        {
            std::lock_guard<std::mutex> lock(dpiCacheMutex_);
            dpiCache_[monitor] = info;
        }
        
        return info;
        
    } catch (...) {
        return GetSystemDPI();
    }
}

DPIInfo UIDPIManager::GetPointDPI(const POINT& point) const {
    try {
        HMONITOR monitor = MonitorFromPoint(point, MONITOR_DEFAULTTONEAREST);
        return GetMonitorDPI(monitor);
    } catch (...) {
        return GetSystemDPI();
    }
}

float UIDPIManager::CalculateScaledValue(float value, const DPIInfo* dpi) const {
    try {
        DPIInfo dpiToUse = dpi ? *dpi : GetSystemDPI();
        return value * dpiToUse.scaleX;
    } catch (...) {
        return value;
    }
}

SIZE UIDPIManager::CalculateScaledSize(const SIZE& size, const DPIInfo* dpi) const {
    try {
        DPIInfo dpiToUse = dpi ? *dpi : GetSystemDPI();
        SIZE scaledSize;
        scaledSize.cx = static_cast<LONG>(std::round(size.cx * dpiToUse.scaleX));
        scaledSize.cy = static_cast<LONG>(std::round(size.cy * dpiToUse.scaleY));
        return scaledSize;
    } catch (...) {
        return size;
    }
}

RECT UIDPIManager::CalculateScaledRect(const RECT& rect, const DPIInfo* dpi) const {
    try {
        DPIInfo dpiToUse = dpi ? *dpi : GetSystemDPI();
        RECT scaledRect;
        scaledRect.left = static_cast<LONG>(std::round(rect.left * dpiToUse.scaleX));
        scaledRect.top = static_cast<LONG>(std::round(rect.top * dpiToUse.scaleY));
        scaledRect.right = static_cast<LONG>(std::round(rect.right * dpiToUse.scaleX));
        scaledRect.bottom = static_cast<LONG>(std::round(rect.bottom * dpiToUse.scaleY));
        return scaledRect;
    } catch (...) {
        return rect;
    }
}

std::vector<MonitorInfo> UIDPIManager::GetAllMonitors() const {
    try {
        // 检查是否需要刷新
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - lastMonitorUpdate_);
        
        if (elapsed.count() > 5) { // 5秒后刷新
            const_cast<UIDPIManager*>(this)->RefreshMonitors();
        }
        
        std::lock_guard<std::mutex> lock(monitorsMutex_);
        return monitors_;
        
    } catch (...) {
        return {};
    }
}

MonitorInfo UIDPIManager::GetPrimaryMonitor() const {
    try {
        auto monitors = GetAllMonitors();
        auto it = std::find_if(monitors.begin(), monitors.end(),
                              [](const MonitorInfo& info) { return info.isPrimary; });
        
        if (it != monitors.end()) {
            return *it;
        }
        
        // 如果没找到主监视器，返回第一个
        if (!monitors.empty()) {
            return monitors[0];
        }
        
        return MonitorInfo();
        
    } catch (...) {
        return MonitorInfo();
    }
}

MonitorInfo UIDPIManager::GetWindowMonitor(HWND window) const {
    try {
        if (!window) {
            return GetPrimaryMonitor();
        }
        
        HMONITOR monitor = MonitorFromWindow(window, MONITOR_DEFAULTTONEAREST);
        auto monitors = GetAllMonitors();
        
        auto it = std::find_if(monitors.begin(), monitors.end(),
                              [monitor](const MonitorInfo& info) { return info.handle == monitor; });
        
        if (it != monitors.end()) {
            return *it;
        }
        
        return GetPrimaryMonitor();
        
    } catch (...) {
        return GetPrimaryMonitor();
    }
}

MonitorInfo UIDPIManager::GetPointMonitor(const POINT& point) const {
    try {
        HMONITOR monitor = MonitorFromPoint(point, MONITOR_DEFAULTTONEAREST);
        auto monitors = GetAllMonitors();
        
        auto it = std::find_if(monitors.begin(), monitors.end(),
                              [monitor](const MonitorInfo& info) { return info.handle == monitor; });
        
        if (it != monitors.end()) {
            return *it;
        }
        
        return GetPrimaryMonitor();
        
    } catch (...) {
        return GetPrimaryMonitor();
    }
}

void UIDPIManager::RefreshMonitors() {
    try {
        EnumerateMonitors();
        NotifyMonitorChange();
        HHBUI_LOG_DEBUG(L"监视器信息已刷新");
    } catch (...) {
        HHBUI_LOG_WARNING(L"监视器信息刷新时发生异常");
    }
}

VoidResult UIDPIManager::HandleDPIChange(HWND window, UINT newDPI, const RECT& suggestedRect) {
    try {
        if (!window) {
            return VoidResult(E_INVALIDARG, L"窗口句柄无效");
        }

        // 获取旧DPI
        DPIInfo oldDPI = GetWindowDPI(window);
        DPIInfo newDPIInfo(newDPI, newDPI);

        // 清理相关缓存
        HMONITOR monitor = MonitorFromWindow(window, MONITOR_DEFAULTTONEAREST);
        {
            std::lock_guard<std::mutex> lock(dpiCacheMutex_);
            dpiCache_.erase(monitor);
        }

        // 触发回调
        DPIChangeEventArgs args;
        args.window = window;
        args.monitor = monitor;
        args.oldDPI = oldDPI;
        args.newDPI = newDPIInfo;
        args.suggestedRect = suggestedRect;

        NotifyDPIChange(args);

        HHBUI_LOG_DEBUG(L"DPI变化处理完成");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"DPI变化处理时发生异常");
    }
}

uint64_t UIDPIManager::RegisterDPIChangeCallback(const DPIChangeCallback& callback) {
    try {
        uint64_t id = GetNextCallbackId();
        std::lock_guard<std::mutex> lock(callbackMutex_);
        dpiChangeCallbacks_[id] = callback;
        return id;
    } catch (...) {
        return 0;
    }
}

uint64_t UIDPIManager::RegisterMonitorChangeCallback(const MonitorChangeCallback& callback) {
    try {
        uint64_t id = GetNextCallbackId();
        std::lock_guard<std::mutex> lock(callbackMutex_);
        monitorChangeCallbacks_[id] = callback;
        return id;
    } catch (...) {
        return 0;
    }
}

bool UIDPIManager::UnregisterCallback(uint64_t callbackId) {
    try {
        std::lock_guard<std::mutex> lock(callbackMutex_);

        auto dpiIt = dpiChangeCallbacks_.find(callbackId);
        if (dpiIt != dpiChangeCallbacks_.end()) {
            dpiChangeCallbacks_.erase(dpiIt);
            return true;
        }

        auto monitorIt = monitorChangeCallbacks_.find(callbackId);
        if (monitorIt != monitorChangeCallbacks_.end()) {
            monitorChangeCallbacks_.erase(monitorIt);
            return true;
        }

        return false;

    } catch (...) {
        return false;
    }
}

void UIDPIManager::ClearCache() {
    try {
        std::lock_guard<std::mutex> lock(dpiCacheMutex_);
        dpiCache_.clear();
        HHBUI_LOG_DEBUG(L"DPI缓存已清理");
    } catch (...) {
        HHBUI_LOG_WARNING(L"DPI缓存清理时发生异常");
    }
}

std::unordered_map<std::string, size_t> UIDPIManager::GetCacheStats() const {
    try {
        std::unordered_map<std::string, size_t> stats;

        {
            std::lock_guard<std::mutex> lock(dpiCacheMutex_);
            stats["dpi_cache_size"] = dpiCache_.size();
        }

        {
            std::lock_guard<std::mutex> lock(monitorsMutex_);
            stats["monitors_count"] = monitors_.size();
        }

        {
            std::lock_guard<std::mutex> lock(callbackMutex_);
            stats["dpi_callbacks_count"] = dpiChangeCallbacks_.size();
            stats["monitor_callbacks_count"] = monitorChangeCallbacks_.size();
        }

        return stats;

    } catch (...) {
        return {};
    }
}

std::wstring UIDPIManager::GenerateDiagnosticReport() const {
    try {
        std::wstringstream ss;

        ss << L"=== DPI管理器诊断报告 ===\n";
        ss << L"初始化状态: " << (initialized_.load() ? L"已初始化" : L"未初始化") << L"\n";
        ss << L"DPI感知状态: " << (dpiAwarenessSet_.load() ? L"已设置" : L"未设置") << L"\n";

        // 系统DPI信息
        auto systemDPI = GetSystemDPI();
        ss << L"系统DPI: " << systemDPI.dpiX << L"x" << systemDPI.dpiY << L"\n";
        ss << L"系统缩放: " << std::fixed << std::setprecision(2)
           << systemDPI.scaleX << L"x" << systemDPI.scaleY << L"\n";

        // 监视器信息
        auto monitors = GetAllMonitors();
        ss << L"监视器数量: " << monitors.size() << L"\n";

        for (size_t i = 0; i < monitors.size(); ++i) {
            const auto& monitor = monitors[i];
            ss << L"监视器 " << (i + 1) << L": ";
            ss << L"DPI=" << monitor.dpiInfo.dpiX << L"x" << monitor.dpiInfo.dpiY;
            ss << L", 缩放=" << std::fixed << std::setprecision(2)
               << monitor.dpiInfo.scaleX << L"x" << monitor.dpiInfo.scaleY;
            ss << L", 主显示器=" << (monitor.isPrimary ? L"是" : L"否");
            ss << L"\n";
        }

        // 缓存统计
        auto stats = GetCacheStats();
        ss << L"缓存统计:\n";
        for (const auto& [key, value] : stats) {
            std::wstring wkey(key.begin(), key.end());
            ss << L"  " << wkey << L": " << value << L"\n";
        }

        // 配置信息
        {
            std::lock_guard<std::mutex> lock(configMutex_);
            ss << L"配置信息:\n";
            ss << L"  自定义缩放: " << config_.customScale << L"\n";
            ss << L"  缩放范围: " << config_.minScale << L" - " << config_.maxScale << L"\n";
            ss << L"  动态DPI: " << (config_.enableDynamicDPI ? L"启用" : L"禁用") << L"\n";
        }

        return ss.str();

    } catch (...) {
        return L"生成诊断报告时发生异常";
    }
}

VoidResult UIDPIManager::ValidateDPISettings() const {
    try {
        // 检查初始化状态
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"DPI管理器未初始化");
        }

        // 检查系统API可用性
        if (!getDpiForMonitor_ && !getDpiForWindow_) {
            HHBUI_LOG_WARNING(L"高级DPI API不可用，功能可能受限");
        }

        // 检查监视器信息
        auto monitors = GetAllMonitors();
        if (monitors.empty()) {
            return VoidResult(E_FAIL, L"未检测到任何监视器");
        }

        // 检查主监视器
        bool hasPrimary = std::any_of(monitors.begin(), monitors.end(),
                                     [](const MonitorInfo& info) { return info.isPrimary; });
        if (!hasPrimary) {
            HHBUI_LOG_WARNING(L"未检测到主监视器");
        }

        // 检查DPI值合理性
        for (const auto& monitor : monitors) {
            if (monitor.dpiInfo.dpiX < 72 || monitor.dpiInfo.dpiX > 480 ||
                monitor.dpiInfo.dpiY < 72 || monitor.dpiInfo.dpiY > 480) {
                HHBUI_LOG_WARNING(L"检测到异常的DPI值: " +
                                 std::to_wstring(monitor.dpiInfo.dpiX) + L"x" +
                                 std::to_wstring(monitor.dpiInfo.dpiY));
            }
        }

        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"DPI设置验证时发生异常");
    }
}

// ==================== 私有方法实现 ====================

VoidResult UIDPIManager::InitializeDPIAwareness() {
    try {
        std::lock_guard<std::mutex> lock(configMutex_);

        bool success = false;

        // 尝试设置DPI感知模式
        if (setProcessDpiAwarenessContext_) {
            switch (config_.awarenessMode) {
            case DPIAwarenessMode::PerMonitorAwareV2:
                success = setProcessDpiAwarenessContext_(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
                if (!success) {
                    // 回退到V1
                    success = setProcessDpiAwarenessContext_(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE);
                }
                break;

            case DPIAwarenessMode::PerMonitorAware:
                success = setProcessDpiAwarenessContext_(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE);
                break;

            case DPIAwarenessMode::SystemAware:
                success = setProcessDpiAwarenessContext_(DPI_AWARENESS_CONTEXT_SYSTEM_AWARE);
                break;

            case DPIAwarenessMode::Unaware:
                success = setProcessDpiAwarenessContext_(DPI_AWARENESS_CONTEXT_UNAWARE);
                break;
            }
        }

        // 如果新API失败，使用旧API
        if (!success && config_.awarenessMode != DPIAwarenessMode::Unaware) {
            success = SetProcessDPIAware() != FALSE;
        }

        dpiAwarenessSet_.store(success);

        if (success) {
            HHBUI_LOG_DEBUG(L"DPI感知模式设置成功");
            return VoidResult();
        } else {
            return VoidResult(E_FAIL, L"DPI感知模式设置失败");
        }

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"DPI感知初始化时发生异常");
    }
}

void UIDPIManager::EnumerateMonitors() {
    try {
        std::lock_guard<std::mutex> lock(monitorsMutex_);
        monitors_.clear();

        // 枚举所有监视器
        EnumDisplayMonitors(nullptr, nullptr, MonitorEnumProc, reinterpret_cast<LPARAM>(this));

        lastMonitorUpdate_ = std::chrono::steady_clock::now();

        HHBUI_LOG_DEBUG(L"枚举到 " + std::to_wstring(monitors_.size()) + L" 个监视器");

    } catch (...) {
        HHBUI_LOG_WARNING(L"监视器枚举时发生异常");
    }
}

BOOL CALLBACK UIDPIManager::MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData) {
    try {
        auto* manager = reinterpret_cast<UIDPIManager*>(dwData);
        if (!manager) return TRUE;

        MonitorInfo info(hMonitor);

        // 获取监视器信息
        MONITORINFOEXW monitorInfo = {};
        monitorInfo.cbSize = sizeof(MONITORINFOEXW);

        if (GetMonitorInfoW(hMonitor, &monitorInfo)) {
            info.workArea = monitorInfo.rcWork;
            info.fullArea = monitorInfo.rcMonitor;
            info.isPrimary = (monitorInfo.dwFlags & MONITORINFOF_PRIMARY) != 0;
            info.deviceName = monitorInfo.szDevice;
        }

        // 获取DPI信息
        info.dpiInfo = manager->GetMonitorDPIInternal(hMonitor);

        manager->monitors_.push_back(info);

        return TRUE;

    } catch (...) {
        return TRUE; // 继续枚举
    }
}

DPIInfo UIDPIManager::GetMonitorDPIInternal(HMONITOR monitor) const {
    try {
        UINT dpiX = USER_DEFAULT_SCREEN_DPI, dpiY = USER_DEFAULT_SCREEN_DPI;

        // 尝试使用Windows 8.1+的API
        if (getDpiForMonitor_) {
            HRESULT hr = getDpiForMonitor_(monitor, 0, &dpiX, &dpiY); // MDT_EFFECTIVE_DPI = 0
            if (SUCCEEDED(hr)) {
                DPIInfo info(dpiX, dpiY);

                // 应用配置
                {
                    std::lock_guard<std::mutex> lock(configMutex_);
                    if (config_.customScale > 0.0f) {
                        info.scaleX = config_.customScale;
                        info.scaleY = config_.customScale;
                    }

                    info.scaleX = std::clamp(info.scaleX, config_.minScale, config_.maxScale);
                    info.scaleY = std::clamp(info.scaleY, config_.minScale, config_.maxScale);

                    info.scaleX = QuantizeScale(info.scaleX);
                    info.scaleY = QuantizeScale(info.scaleY);
                }

                return info;
            }
        }

        // 回退到系统DPI
        return GetSystemDPI();

    } catch (...) {
        return GetSystemDPI();
    }
}

float UIDPIManager::QuantizeScale(float scale) const {
    // 量化到0.25的倍数以减少渲染误差
    const float quantizeStep = 0.25f;
    return std::round(scale / quantizeStep) * quantizeStep;
}

void UIDPIManager::NotifyDPIChange(const DPIChangeEventArgs& args) {
    try {
        std::lock_guard<std::mutex> lock(callbackMutex_);
        for (const auto& [id, callback] : dpiChangeCallbacks_) {
            try {
                callback(args);
            } catch (...) {
                // 忽略回调异常
            }
        }
    } catch (...) {
        // 忽略通知异常
    }
}

void UIDPIManager::NotifyMonitorChange() {
    try {
        std::vector<MonitorInfo> currentMonitors;
        {
            std::lock_guard<std::mutex> lock(monitorsMutex_);
            currentMonitors = monitors_;
        }

        std::lock_guard<std::mutex> lock(callbackMutex_);
        for (const auto& [id, callback] : monitorChangeCallbacks_) {
            try {
                callback(currentMonitors);
            } catch (...) {
                // 忽略回调异常
            }
        }
    } catch (...) {
        // 忽略通知异常
    }
}

uint64_t UIDPIManager::GetNextCallbackId() {
    return nextCallbackId_.fetch_add(1, std::memory_order_relaxed);
}

} // namespace HHBUI
