/**
 * @file font_manager.h
 * @brief 字体管理器 - 高性能字体缓存和管理系统
 * <AUTHOR> Team
 * @version 2.0.0
 * @date 2025-01-30
 */

#pragma once

#include <memory>
#include <unordered_map>
#include <vector>
#include <string>
#include <functional>
#include <mutex>
#include <atomic>
#include <chrono>

namespace HHBUI {

// 前向声明
class UIFont;

// ==================== 字体相关结构体 ====================

/// 字体缓存键
struct FontCacheKey {
    std::wstring faceName;
    int32_t size;
    uint32_t style;
    float dpiScale;
    
    FontCacheKey() = default;
    FontCacheKey(const std::wstring& face, int32_t sz, uint32_t st, float dpi)
        : faceName(face), size(sz), style(st), dpiScale(dpi) {}
    
    bool operator==(const FontCacheKey& other) const noexcept {
        return faceName == other.faceName && 
               size == other.size && 
               style == other.style &&
               std::abs(dpiScale - other.dpiScale) < 0.001f;
    }
    
    bool operator!=(const FontCacheKey& other) const noexcept {
        return !(*this == other);
    }
};

/// 字体缓存项
struct FontCacheItem {
    std::shared_ptr<UIFont> font;
    std::chrono::steady_clock::time_point lastAccess;
    uint32_t accessCount;
    size_t memoryUsage;
    
    FontCacheItem() : accessCount(0), memoryUsage(0) {}
    FontCacheItem(std::shared_ptr<UIFont> f, size_t memory = 0)
        : font(f), lastAccess(std::chrono::steady_clock::now()), accessCount(1), memoryUsage(memory) {}
};

/// 字体文件信息
struct FontFileInfo {
    std::wstring filePath;
    std::vector<uint8_t> fontData;
    std::vector<std::wstring> fontFaces;
    bool isMemoryFont;
    std::chrono::steady_clock::time_point loadTime;
    
    FontFileInfo() : isMemoryFont(false) {}
};

/// 字体统计信息
struct FontStatistics {
    size_t totalFonts = 0;
    size_t cachedFonts = 0;
    size_t memoryFonts = 0;
    size_t totalMemoryUsage = 0;
    size_t cacheHits = 0;
    size_t cacheMisses = 0;
    double hitRatio = 0.0;
    
    FontStatistics() = default;
};

// ==================== 回调函数类型 ====================

/// 字体加载回调
using FontLoadCallback = std::function<void(const std::wstring& fontFace, bool success)>;

/// 字体缓存清理回调
using FontCacheCleanupCallback = std::function<void(size_t removedCount, size_t freedMemory)>;

// ==================== 字体缓存键哈希函数 ====================

struct FontCacheKeyHash {
    size_t operator()(const FontCacheKey& key) const noexcept {
        size_t h1 = std::hash<std::wstring>{}(key.faceName);
        size_t h2 = std::hash<int32_t>{}(key.size);
        size_t h3 = std::hash<uint32_t>{}(key.style);
        size_t h4 = std::hash<float>{}(key.dpiScale);
        
        // 组合哈希值
        return h1 ^ (h2 << 1) ^ (h3 << 2) ^ (h4 << 3);
    }
};

// ==================== UIFontManager类 ====================

/**
 * @brief 字体管理器 - 提供高性能字体缓存和管理
 * 
 * 特性：
 * - 智能字体缓存
 * - 内存字体支持
 * - DPI感知字体缩放
 * - 自动垃圾回收
 * - 字体预加载
 * - 性能统计和监控
 */
class TOAPI UIFontManager {
public:
    UIFontManager();
    ~UIFontManager();
    
    // 禁用拷贝和移动
    UIFontManager(const UIFontManager&) = delete;
    UIFontManager(UIFontManager&&) = delete;
    UIFontManager& operator=(const UIFontManager&) = delete;
    UIFontManager& operator=(UIFontManager&&) = delete;
    
    // ==================== 初始化和配置 ====================
    
    /**
     * @brief 初始化字体管理器
     * @param config 字体配置
     * @return 初始化结果
     */
    VoidResult Initialize(const FontConfig& config);
    
    /**
     * @brief 关闭字体管理器
     */
    void Shutdown();
    
    /**
     * @brief 更新配置
     * @param config 新配置
     * @return 更新结果
     */
    VoidResult UpdateConfig(const FontConfig& config);
    
    // ==================== 字体创建和获取 ====================
    
    /**
     * @brief 创建字体
     * @param config 字体配置
     * @param dpiScale DPI缩放系数（可选）
     * @return 字体对象
     */
    Result<std::shared_ptr<UIFont>> CreateFont(const FontConfig& config, float dpiScale = 0.0f);
    
    /**
     * @brief 获取默认字体
     * @return 默认字体对象
     */
    std::shared_ptr<UIFont> GetDefaultFont() const;
    
    /**
     * @brief 设置默认字体
     * @param config 字体配置
     * @return 设置结果
     */
    VoidResult SetDefaultFont(const FontConfig& config);
    
    /**
     * @brief 预加载字体
     * @param configs 字体配置列表
     * @return 预加载结果
     */
    VoidResult PreloadFonts(const std::vector<FontConfig>& configs);
    
    // ==================== 字体文件管理 ====================
    
    /**
     * @brief 从文件加载字体
     * @param filePath 字体文件路径
     * @return 加载结果
     */
    VoidResult LoadFontFromFile(const std::wstring& filePath);
    
    /**
     * @brief 从内存加载字体
     * @param fontData 字体数据
     * @param dataSize 数据大小
     * @param fontFace 字体名称
     * @return 加载结果
     */
    VoidResult LoadFontFromMemory(const void* fontData, size_t dataSize, const std::wstring& fontFace);
    
    /**
     * @brief 卸载字体文件
     * @param fontFace 字体名称
     * @return 卸载结果
     */
    VoidResult UnloadFont(const std::wstring& fontFace);
    
    /**
     * @brief 获取已加载的字体列表
     * @return 字体名称列表
     */
    std::vector<std::wstring> GetLoadedFonts() const;
    
    /**
     * @brief 检查字体是否可用
     * @param fontFace 字体名称
     * @return 是否可用
     */
    bool IsFontAvailable(const std::wstring& fontFace) const;
    
    // ==================== 缓存管理 ====================
    
    /**
     * @brief 清理字体缓存
     * @param forceCleanup 是否强制清理
     * @return 清理结果
     */
    VoidResult CleanupCache(bool forceCleanup = false);
    
    /**
     * @brief 设置缓存大小限制
     * @param maxCacheSize 最大缓存大小（字节）
     * @param maxCacheCount 最大缓存数量
     */
    void SetCacheLimits(size_t maxCacheSize, size_t maxCacheCount);
    
    /**
     * @brief 预热缓存
     * @param commonConfigs 常用字体配置
     * @return 预热结果
     */
    VoidResult WarmupCache(const std::vector<FontConfig>& commonConfigs);
    
    /**
     * @brief 获取缓存统计信息
     * @return 缓存统计
     */
    std::unordered_map<std::string, size_t> GetCacheStats() const;
    
    // ==================== 字体查询和枚举 ====================
    
    /**
     * @brief 枚举系统字体
     * @return 系统字体列表
     */
    std::vector<std::wstring> EnumerateSystemFonts() const;
    
    /**
     * @brief 查找相似字体
     * @param fontFace 目标字体名称
     * @param maxResults 最大结果数量
     * @return 相似字体列表
     */
    std::vector<std::wstring> FindSimilarFonts(const std::wstring& fontFace, size_t maxResults = 5) const;
    
    /**
     * @brief 获取字体信息
     * @param fontFace 字体名称
     * @return 字体信息
     */
    std::optional<FontFileInfo> GetFontInfo(const std::wstring& fontFace) const;
    
    // ==================== 性能监控 ====================
    
    /**
     * @brief 获取字体统计信息
     * @return 统计信息
     */
    FontStatistics GetStatistics() const;
    
    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();
    
    /**
     * @brief 生成性能报告
     * @return 性能报告
     */
    std::wstring GeneratePerformanceReport() const;
    
    // ==================== 回调管理 ====================
    
    /**
     * @brief 注册字体加载回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterLoadCallback(const FontLoadCallback& callback);
    
    /**
     * @brief 注册缓存清理回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterCleanupCallback(const FontCacheCleanupCallback& callback);
    
    /**
     * @brief 取消注册回调
     * @param callbackId 回调ID
     * @return 取消结果
     */
    bool UnregisterCallback(uint64_t callbackId);
    
    // ==================== 调试和诊断 ====================
    
    /**
     * @brief 生成诊断报告
     * @return 诊断报告
     */
    std::wstring GenerateDiagnosticReport() const;
    
    /**
     * @brief 验证字体管理器状态
     * @return 验证结果
     */
    VoidResult ValidateState() const;
    
    /**
     * @brief 导出字体缓存信息
     * @param filePath 导出文件路径
     * @return 导出结果
     */
    VoidResult ExportCacheInfo(const std::wstring& filePath) const;

private:
    // ==================== 私有方法 ====================
    
    /// 创建字体缓存键
    FontCacheKey CreateCacheKey(const FontConfig& config, float dpiScale) const;
    
    /// 创建字体对象
    std::shared_ptr<UIFont> CreateFontInternal(const FontConfig& config, float dpiScale);
    
    /// 添加到缓存
    void AddToCache(const FontCacheKey& key, std::shared_ptr<UIFont> font, size_t memoryUsage = 0);
    
    /// 从缓存获取
    std::shared_ptr<UIFont> GetFromCache(const FontCacheKey& key);
    
    /// 检查缓存限制
    void CheckCacheLimits();
    
    /// 执行缓存清理
    size_t PerformCacheCleanup(bool forceCleanup);
    
    /// 计算字体内存使用
    size_t CalculateFontMemoryUsage(const std::shared_ptr<UIFont>& font) const;
    
    /// 触发加载回调
    void NotifyFontLoad(const std::wstring& fontFace, bool success);
    
    /// 触发清理回调
    void NotifyCleanup(size_t removedCount, size_t freedMemory);
    
    /// 获取下一个回调ID
    uint64_t GetNextCallbackId();
    
    // ==================== 私有成员变量 ====================
    
    /// 配置
    FontConfig config_;
    mutable std::mutex configMutex_;
    
    /// 字体缓存
    std::unordered_map<FontCacheKey, FontCacheItem, FontCacheKeyHash> fontCache_;
    mutable std::mutex cacheMutex_;
    
    /// 字体文件管理
    std::unordered_map<std::wstring, FontFileInfo> loadedFonts_;
    mutable std::mutex fontsMutex_;
    
    /// 默认字体
    std::shared_ptr<UIFont> defaultFont_;
    mutable std::mutex defaultFontMutex_;
    
    /// 缓存限制
    std::atomic<size_t> maxCacheSize_;
    std::atomic<size_t> maxCacheCount_;
    std::atomic<size_t> currentCacheSize_;
    
    /// 统计信息
    mutable std::atomic<size_t> cacheHits_;
    mutable std::atomic<size_t> cacheMisses_;
    mutable std::atomic<size_t> totalMemoryUsage_;
    
    /// 回调管理
    std::atomic<uint64_t> nextCallbackId_;
    std::unordered_map<uint64_t, FontLoadCallback> loadCallbacks_;
    std::unordered_map<uint64_t, FontCacheCleanupCallback> cleanupCallbacks_;
    mutable std::mutex callbackMutex_;
    
    /// 状态标志
    std::atomic<bool> initialized_;
    
    /// 垃圾回收
    std::chrono::steady_clock::time_point lastCleanup_;
    std::chrono::milliseconds cleanupInterval_;
};

} // namespace HHBUI
