﻿/**
 * @file engine.cpp
 * @brief HHBUI引擎核心实现 - 现代C++17重构版本
 * <AUTHOR> Team
 * @version 2.0.0
 * @date 2025-01-30
 *
 * 特性：
 * - 线程安全的单例模式
 * - RAII资源管理
 * - 异步初始化支持
 * - 完整的错误处理和恢复机制
 * - 高性能资源管理和缓存
 * - 动态DPI感知
 * - 插件系统支持
 * - 详细的性能监控和分析
 */

#include "pch.h"
#include "engine.h"
#include "common/winapi.h"
#include "common/Exception.h"
#include "renderd2d.h"
#include "element/wnd.h"
#include "element/font.h"

#include <thread>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <filesystem>

namespace HHBUI {

// ==================== 静态成员初始化 ====================

std::shared_ptr<UIEngine> UIEngine::instance_ = nullptr;
std::mutex UIEngine::instanceMutex_;
std::weak_ptr<UIEngine> UIEngine::legacyInstance_;

// ==================== 辅助函数 ====================

namespace {
    /// 检查Windows版本
    bool IsWindows10OrLater() noexcept {
        try {
            typedef void(__stdcall* NTPROC)(DWORD*, DWORD*, DWORD*);
            HINSTANCE hinst = LoadLibraryW(L"ntdll.dll");
            if (!hinst) return false;

            NTPROC proc = reinterpret_cast<NTPROC>(GetProcAddress(hinst, "RtlGetNtVersionNumbers"));
            if (!proc) {
                FreeLibrary(hinst);
                return false;
            }

            DWORD dwMajor = 0, dwMinor = 0, dwBuildNumber = 0;
            proc(&dwMajor, &dwMinor, &dwBuildNumber);
            FreeLibrary(hinst);

            return (dwMajor >= 10);
        } catch (...) {
            return false;
        }
    }

    /// 获取系统DPI信息
    struct DPIInfo {
        float scaleX = 1.0f;
        float scaleY = 1.0f;
        uint32_t dpiX = USER_DEFAULT_SCREEN_DPI;
        uint32_t dpiY = USER_DEFAULT_SCREEN_DPI;
    };

    DPIInfo GetSystemDPIInfo(float customScale = 0.0f) noexcept {
        DPIInfo info;

        try {
            HDC hdcMeasure = ::GetDC(nullptr);
            if (hdcMeasure) {
                info.dpiX = static_cast<uint32_t>(GetDeviceCaps(hdcMeasure, LOGPIXELSX));
                info.dpiY = static_cast<uint32_t>(GetDeviceCaps(hdcMeasure, LOGPIXELSY));
                ::ReleaseDC(nullptr, hdcMeasure);

                info.scaleX = static_cast<float>(info.dpiX) / USER_DEFAULT_SCREEN_DPI;
                info.scaleY = static_cast<float>(info.dpiY) / USER_DEFAULT_SCREEN_DPI;

                // 应用自定义缩放
                if (customScale > 0.0f) {
                    info.scaleX = customScale;
                    info.scaleY = customScale;
                }

                // 限制最大缩放比例
                if (info.scaleX >= 2.0f) {
                    info.scaleX = 1.25f;
                }
                if (info.scaleY >= 2.0f) {
                    info.scaleY = 1.25f;
                }

                // 量化缩放比例以减少渲染误差
                const float quantizeStep = 0.25f;
                float fmodX = std::fmodf(info.scaleX, quantizeStep);
                float fmodY = std::fmodf(info.scaleY, quantizeStep);

                if (fmodX != 0.0f) {
                    info.scaleX -= fmodX;
                }
                if (fmodY != 0.0f) {
                    info.scaleY -= fmodY;
                }
            }
        } catch (...) {
            // 使用默认值
        }

        return info;
    }

    /// 生成唯一ID
    uint64_t GenerateUniqueId() noexcept {
        static std::atomic<uint64_t> counter{1};
        return counter.fetch_add(1, std::memory_order_relaxed);
    }

    /// 格式化时间戳
    std::wstring FormatTimestamp(const std::chrono::system_clock::time_point& timePoint) {
        auto time_t = std::chrono::system_clock::to_time_t(timePoint);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            timePoint.time_since_epoch()) % 1000;

        std::wstringstream ss;
        ss << std::put_time(std::localtime(&time_t), L"%Y-%m-%d %H:%M:%S");
        ss << L"." << std::setfill(L'0') << std::setw(3) << ms.count();
        return ss.str();
    }
}
// ==================== UIEngine实现 ====================

UIEngine::UIEngine()
    : state_(EngineState::Uninitialized)
    , nextCallbackId_(1)
    , comInitialized_(false)
    , startTime_(std::chrono::steady_clock::now()) {

    // 初始化时间戳
    initTime_ = startTime_;
}

UIEngine::~UIEngine() {
    try {
        if (state_.load() != EngineState::Uninitialized &&
            state_.load() != EngineState::Stopped) {
            ShutdownInternal(true); // 强制关闭
        }
    } catch (...) {
        // 析构函数中不抛出异常
    }
}

EngineHandle UIEngine::GetInstance() {
    std::lock_guard<std::mutex> lock(instanceMutex_);

    if (!instance_) {
        // 使用make_shared的私有构造函数技巧
        struct MakeSharedEnabler : public UIEngine {};
        instance_ = std::make_shared<MakeSharedEnabler>();
        legacyInstance_ = instance_;
    }

    return instance_;
}

bool UIEngine::HasInstance() noexcept {
    std::lock_guard<std::mutex> lock(instanceMutex_);
    return instance_ != nullptr;
}

VoidResult UIEngine::Initialize(const EngineInitConfig& config) {
    try {
        HHBUI_LOG_INFO(L"开始初始化HHBUI引擎...");

        // 检查当前状态
        EngineState currentState = state_.load();
        if (currentState != EngineState::Uninitialized) {
            return VoidResult(E_FAIL, L"引擎已经初始化或正在初始化中");
        }

        // 设置初始化状态
        SetState(EngineState::Initializing);

        // 执行初始化
        auto result = InitializeInternal(config);

        if (result.IsSuccess()) {
            SetState(EngineState::Initialized);
            HHBUI_LOG_INFO(L"HHBUI引擎初始化成功");
        } else {
            SetState(EngineState::Error);
            HHBUI_LOG_ERROR(L"HHBUI引擎初始化失败: " + result.GetErrorMessage());
        }

        return result;

    } catch (const EngineException& e) {
        SetState(EngineState::Error);
        std::wstring message = L"引擎初始化异常: " + e.GetMessage();
        HHBUI_LOG_ERROR(message);
        return VoidResult(e.GetErrorCode(), message);
    } catch (const std::exception& e) {
        SetState(EngineState::Error);
        std::wstring message = L"引擎初始化标准异常: ";
        // 转换std::exception消息
        int size = MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, nullptr, 0);
        if (size > 0) {
            std::wstring wstr(size - 1, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, &wstr[0], size);
            message += wstr;
        }
        HHBUI_LOG_ERROR(message);
        return VoidResult(E_UNEXPECTED, message);
    } catch (...) {
        SetState(EngineState::Error);
        std::wstring message = L"引擎初始化未知异常";
        HHBUI_LOG_ERROR(message);
        return VoidResult(E_UNEXPECTED, message);
    }
}

std::future<VoidResult> UIEngine::InitializeAsync(const EngineInitConfig& config) {
    return std::async(std::launch::async, [this, config]() {
        return Initialize(config);
    });
}

VoidResult UIEngine::Shutdown(bool forceShutdown) {
    try {
        HHBUI_LOG_INFO(forceShutdown ? L"开始强制关闭HHBUI引擎..." : L"开始正常关闭HHBUI引擎...");

        EngineState currentState = state_.load();
        if (currentState == EngineState::Uninitialized ||
            currentState == EngineState::Stopped) {
            return VoidResult(); // 已经关闭
        }

        SetState(EngineState::Stopping);

        auto result = ShutdownInternal(forceShutdown);

        if (result.IsSuccess()) {
            SetState(EngineState::Stopped);
            HHBUI_LOG_INFO(L"HHBUI引擎关闭成功");
        } else {
            SetState(EngineState::Error);
            HHBUI_LOG_ERROR(L"HHBUI引擎关闭失败: " + result.GetErrorMessage());
        }

        return result;

    } catch (const EngineException& e) {
        SetState(EngineState::Error);
        std::wstring message = L"引擎关闭异常: " + e.GetMessage();
        HHBUI_LOG_ERROR(message);
        return VoidResult(e.GetErrorCode(), message);
    } catch (...) {
        SetState(EngineState::Error);
        std::wstring message = L"引擎关闭未知异常";
        HHBUI_LOG_ERROR(message);
        return VoidResult(E_UNEXPECTED, message);
    }
}

std::future<VoidResult> UIEngine::ShutdownAsync(bool forceShutdown) {
    return std::async(std::launch::async, [this, forceShutdown]() {
        return Shutdown(forceShutdown);
    });
}

VoidResult UIEngine::Restart(const std::optional<EngineInitConfig>& newConfig) {
    HHBUI_LOG_INFO(L"重启HHBUI引擎...");

    // 先关闭
    auto shutdownResult = Shutdown(false);
    if (shutdownResult.IsFailure()) {
        return shutdownResult;
    }

    // 等待一小段时间确保资源完全释放
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 重新初始化
    EngineInitConfig configToUse = newConfig.value_or(
        config_ ? *config_ : EngineInitConfig{});

    return Initialize(configToUse);
}

// ==================== 状态查询方法 ====================

EngineState UIEngine::GetState() const noexcept {
    return state_.load();
}

bool UIEngine::IsInitialized() const noexcept {
    EngineState currentState = state_.load();
    return currentState == EngineState::Initialized ||
           currentState == EngineState::Running ||
           currentState == EngineState::Paused;
}

bool UIEngine::IsRunning() const noexcept {
    return state_.load() == EngineState::Running;
}

bool UIEngine::IsDebugMode() const noexcept {
    if (config_) {
        std::shared_lock<std::shared_mutex> lock(configMutex_);
        return config_->debugConfig.enableDebugMode;
    }
    return false;
}

std::wstring UIEngine::GetVersion() noexcept {
    return HHBUI_VERSION;
}

uint64_t UIEngine::GetVersionNumber() noexcept {
    return HHBUI_VERSION_NUM;
}

double UIEngine::GetRunningTime() const noexcept {
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::duration<double>>(now - startTime_);
    return duration.count();
}

// ==================== 兼容性接口实现 ====================

HRESULT HHBUI::UIEngine::Init(info_Init* info)
{
{
    try {
        // 转换旧配置到新配置
        EngineInitConfig config;

        if (info) {
            config.renderConfig.deviceIndex = info->device;
            config.dpiConfig.customScale = info->dwScaledpi;
            config.debugConfig.enableDebugMode = info->dwDebug != FALSE;

            if (info->hInstance) {
                config.hInstance = info->hInstance;
            }

            if (info->default_font_Face || info->default_font_Size || info->default_font_Style) {
                if (info->default_font_Face) {
                    config.fontConfig.faceName = info->default_font_Face;
                }
                if (info->default_font_Size > 0) {
                    config.fontConfig.size = info->default_font_Size;
                } else {
                    config.fontConfig.size = 15; // 默认大小
                }
                config.fontConfig.style = info->default_font_Style;
            }
        }

        // 获取引擎实例并初始化
        auto engine = GetInstance();
        auto result = engine->Initialize(config);

        return result.IsSuccess() ? S_OK : result.GetErrorCode();

    } catch (const EngineException& e) {
        return e.GetErrorCode();
    } catch (...) {
        return E_UNEXPECTED;
    }

}

HRESULT HHBUI::UIEngine::UnInit() {
    try {
        auto engine = legacyInstance_.lock();
        if (engine) {
            auto result = engine->Shutdown(false);
            return result.IsSuccess() ? S_OK : result.GetErrorCode();
        }
        return EE_NOREADY; // 引擎未初始化
    } catch (const EngineException& e) {
        return e.GetErrorCode();
    } catch (...) {
        return E_UNEXPECTED;
    }
}

BOOL HHBUI::UIEngine::QueryDebug() {
    try {
        auto engine = legacyInstance_.lock();
        if (engine) {
            return engine->IsDebugMode() ? TRUE : FALSE;
        }
        return FALSE;
    } catch (...) {
        return FALSE;
    }
}

BOOL HHBUI::UIEngine::QueryInit() {
    try {
        auto engine = legacyInstance_.lock();
        if (engine) {
            return engine->IsInitialized() ? TRUE : FALSE;
        }
        return FALSE;
    } catch (...) {
        return FALSE;
    }
}

FLOAT HHBUI::UIEngine::fScale(FLOAT n) {
    try {
        auto engine = legacyInstance_.lock();
        if (engine) {
            return engine->CalculateScaledValue(n);
        }
        // 如果引擎未初始化，使用系统DPI
        auto dpiInfo = GetSystemDPIInfo();
        return n * dpiInfo.scaleX;
    } catch (...) {
        return n; // 返回原值
    }
}

FLOAT HHBUI::UIEngine::GetDefaultScale() {
    try {
        auto engine = legacyInstance_.lock();
        if (engine) {
            return engine->GetDPIScale();
        }
        // 如果引擎未初始化，使用系统DPI
        auto dpiInfo = GetSystemDPIInfo();
        return dpiInfo.scaleX;
    } catch (...) {
        return 1.0f; // 返回默认缩放
    }
}

FLOAT HHBUI::UIEngine::GetTime() {
    try {
        auto engine = legacyInstance_.lock();
        if (engine) {
            return static_cast<FLOAT>(engine->GetRunningTime());
        }
        // 如果引擎未初始化，使用静态计时器
        static auto start_time = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::duration<float>>(now - start_time);
        return elapsed.count();
    } catch (...) {
        return 0.0f;
    }
}

LPCWSTR HHBUI::UIEngine::GetVersion() {
    return HHBUI_VERSION;
}

// ==================== 核心初始化实现 ====================

VoidResult UIEngine::InitializeInternal(const EngineInitConfig& config) {
    try {
        HHBUI_LOG_DEBUG(L"开始内部初始化流程...");

        // 1. 验证配置
        auto validateResult = ValidateConfig(config);
        if (validateResult.IsFailure()) {
            return validateResult;
        }

        // 2. 保存配置
        config_ = std::make_shared<EngineInitConfig>(config);

        // 3. 检查系统兼容性
        auto compatResult = CheckSystemCompatibility();
        if (compatResult.IsFailure()) {
            return compatResult;
        }

        // 4. 初始化COM
        auto comResult = InitializeCOM();
        if (comResult.IsFailure()) {
            return comResult;
        }

        // 5. 初始化DPI感知
        auto dpiResult = InitializeDPIAwareness(config.dpiConfig);
        if (dpiResult.IsFailure()) {
            ShutdownCOM(); // 回滚
            return dpiResult;
        }

        // 6. 初始化子系统
        auto subsystemResult = InitializeSubsystems(config);
        if (subsystemResult.IsFailure()) {
            ShutdownCOM(); // 回滚
            return subsystemResult;
        }

        // 7. 应用配置
        auto applyResult = ApplyConfig(config);
        if (applyResult.IsFailure()) {
            ShutdownSubsystems(true); // 回滚
            ShutdownCOM();
            return applyResult;
        }

        // 8. 注册窗口类
        if (!UIWnd::RegClass(L"Hhbui.WindowClass.UI", 0, 0)) {
            HHBUI_LOG_WARNING(L"窗口类注册失败，但继续初始化");
        }

        HHBUI_LOG_DEBUG(L"内部初始化流程完成");
        return VoidResult();

    } catch (const InitializationException& e) {
        HHBUI_LOG_ERROR(L"初始化异常: " + e.GetMessage());
        return VoidResult(e.GetErrorCode(), e.GetMessage());
    } catch (const std::exception& e) {
        std::wstring message = L"初始化标准异常: ";
        // 转换异常消息
        int size = MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, nullptr, 0);
        if (size > 0) {
            std::wstring wstr(size - 1, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, &wstr[0], size);
            message += wstr;
        }
        HHBUI_LOG_ERROR(message);
        return VoidResult(E_UNEXPECTED, message);
    } catch (...) {
        std::wstring message = L"初始化未知异常";
        HHBUI_LOG_ERROR(message);
        return VoidResult(E_UNEXPECTED, message);
    }
}

VoidResult UIEngine::ShutdownInternal(bool forceShutdown) {
    try {
        HHBUI_LOG_DEBUG(forceShutdown ? L"开始强制内部关闭流程..." : L"开始正常内部关闭流程...");

        // 1. 关闭子系统
        auto subsystemResult = ShutdownSubsystems(forceShutdown);
        if (subsystemResult.IsFailure() && !forceShutdown) {
            HHBUI_LOG_WARNING(L"子系统关闭失败: " + subsystemResult.GetErrorMessage());
        }

        // 2. 关闭COM
        ShutdownCOM();

        // 3. 清理配置
        {
            std::unique_lock<std::shared_mutex> lock(configMutex_);
            config_.reset();
        }

        // 4. 清理回调
        {
            std::unique_lock<std::shared_mutex> lock(callbackMutex_);
            stateCallbacks_.clear();
            errorCallbacks_.clear();
            performanceCallbacks_.clear();
        }

        HHBUI_LOG_DEBUG(L"内部关闭流程完成");
        return VoidResult();

    } catch (const std::exception& e) {
        std::wstring message = L"关闭异常: ";
        int size = MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, nullptr, 0);
        if (size > 0) {
            std::wstring wstr(size - 1, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, &wstr[0], size);
            message += wstr;
        }
        HHBUI_LOG_ERROR(message);
        return VoidResult(E_UNEXPECTED, message);
    } catch (...) {
        std::wstring message = L"关闭未知异常";
        HHBUI_LOG_ERROR(message);
        return VoidResult(E_UNEXPECTED, message);
    }
}

VoidResult UIEngine::ValidateConfig(const EngineInitConfig& config) const {
    try {
        // 验证DPI配置
        if (config.dpiConfig.customScale < 0.0f || config.dpiConfig.customScale > 10.0f) {
            return VoidResult(E_INVALIDARG, L"DPI缩放值超出有效范围 (0.0-10.0)");
        }

        if (config.dpiConfig.minScale <= 0.0f || config.dpiConfig.maxScale <= 0.0f ||
            config.dpiConfig.minScale > config.dpiConfig.maxScale) {
            return VoidResult(E_INVALIDARG, L"DPI缩放范围配置无效");
        }

        // 验证字体配置
        if (config.fontConfig.size <= 0 || config.fontConfig.size > 1000) {
            return VoidResult(E_INVALIDARG, L"字体大小超出有效范围 (1-1000)");
        }

        // 验证性能配置
        if (config.performanceConfig.targetFPS == 0 || config.performanceConfig.targetFPS > 1000) {
            return VoidResult(E_INVALIDARG, L"目标FPS超出有效范围 (1-1000)");
        }

        if (config.performanceConfig.memoryPoolSize == 0) {
            return VoidResult(E_INVALIDARG, L"内存池大小不能为0");
        }

        // 验证线程配置
        if (config.threadConfig.workerThreadCount > 64) {
            return VoidResult(E_INVALIDARG, L"工作线程数量过多 (最大64)");
        }

        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"配置验证时发生异常");
    }
}

VoidResult UIEngine::CheckSystemCompatibility() const {
    try {
        // 检查Windows版本
        if (!IsWindows10OrLater()) {
            HHBUI_LOG_WARNING(L"检测到Windows 10以下版本，某些功能可能不可用");
        }

        // 检查必要的DLL
        std::vector<std::wstring> requiredDlls = {
            L"d2d1.dll",
            L"dwrite.dll",
            L"d3d11.dll",
            L"dxgi.dll"
        };

        for (const auto& dll : requiredDlls) {
            HMODULE hMod = LoadLibraryW(dll.c_str());
            if (!hMod) {
                return VoidResult(EE_LOST_NECESSARY, L"缺少必要的系统库: " + dll);
            }
            FreeLibrary(hMod);
        }

        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"系统兼容性检查时发生异常");
    }
}

VoidResult UIEngine::InitializeCOM() {
    try {
        if (comInitialized_.load()) {
            return VoidResult(); // 已经初始化
        }

        HRESULT hr = CoInitialize(nullptr);
        if (FAILED(hr) && hr != RPC_E_CHANGED_MODE) {
            return VoidResult(hr, L"COM初始化失败");
        }

        comInitialized_.store(true);
        HHBUI_LOG_DEBUG(L"COM初始化成功");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"COM初始化时发生异常");
    }
}

void UIEngine::ShutdownCOM() {
    try {
        if (comInitialized_.load()) {
            CoUninitialize();
            comInitialized_.store(false);
            HHBUI_LOG_DEBUG(L"COM已关闭");
        }
    } catch (...) {
        HHBUI_LOG_WARNING(L"COM关闭时发生异常");
    }
}

VoidResult UIEngine::InitializeDPIAwareness(const DPIConfig& config) {
    try {
        bool isWin10 = IsWindows10OrLater();

        if (isWin10 && config.awarenessMode == DPIAwarenessMode::PerMonitorAwareV2) {
            if (!SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2)) {
                HHBUI_LOG_WARNING(L"设置Per-Monitor DPI Aware V2失败，尝试V1模式");
                if (!SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE)) {
                    HHBUI_LOG_WARNING(L"设置Per-Monitor DPI Aware V1失败，使用系统DPI感知");
                    SetProcessDPIAware();
                }
            }
        } else if (config.awarenessMode == DPIAwarenessMode::PerMonitorAware) {
            if (isWin10) {
                if (!SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE)) {
                    SetProcessDPIAware();
                }
            } else {
                SetProcessDPIAware();
            }
        } else if (config.awarenessMode == DPIAwarenessMode::SystemAware) {
            SetProcessDPIAware();
        }
        // Unaware模式不需要设置

        HHBUI_LOG_DEBUG(L"DPI感知模式设置完成");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"DPI感知初始化时发生异常");
    }
}

VoidResult UIEngine::InitializeSubsystems(const EngineInitConfig& config) {
    try {
        HHBUI_LOG_DEBUG(L"开始初始化子系统...");

        // 1. 初始化WinAPI子系统
        HINSTANCE hInstance = config.hInstance;
        if (!hInstance) {
            hInstance = GetModuleHandleW(nullptr);
        }

        if (FAILED(UIWinApi::Init(hInstance))) {
            return VoidResult(E_FAIL, L"WinAPI子系统初始化失败");
        }

        // 2. 初始化渲染系统
        auto renderResult = InitializeRenderSystem(config.renderConfig);
        if (renderResult.IsFailure()) {
            UIWinApi::UnInit();
            return renderResult;
        }

        // 3. 初始化DPI管理器
        dpiManager_ = std::make_shared<UIDPIManager>();
        auto dpiInitResult = dpiManager_->Initialize(config.dpiConfig);
        if (dpiInitResult.IsFailure()) {
            UIDrawContext::UnInit();
            UIWinApi::UnInit();
            return dpiInitResult;
        }

        // 4. 初始化字体管理器
        fontManager_ = std::make_shared<UIFontManager>();
        auto fontInitResult = fontManager_->Initialize(config.fontConfig);
        if (fontInitResult.IsFailure()) {
            dpiManager_->Shutdown();
            dpiManager_.reset();
            UIDrawContext::UnInit();
            UIWinApi::UnInit();
            return fontInitResult;
        }

        // 5. 初始化线程系统
        auto threadResult = InitializeThreadSystem(config.threadConfig);
        if (threadResult.IsFailure()) {
            fontManager_->Shutdown();
            fontManager_.reset();
            dpiManager_->Shutdown();
            dpiManager_.reset();
            UIDrawContext::UnInit();
            UIWinApi::UnInit();
            return threadResult;
        }

        // 5. 设置图标
        if (hInstance) {
            TCHAR szFilePath[MAX_PATH + 1];
            if (GetModuleFileNameW(hInstance, szFilePath, MAX_PATH)) {
                UIWinApi::ToList.hIcon = ExtractIconW(hInstance, szFilePath, 0);
                UIWinApi::ToList.hIconsm = ExtractIconW(hInstance, szFilePath, 0);
            }
            UIWinApi::ToList.engine_instance = hInstance;
        }

        HHBUI_LOG_DEBUG(L"子系统初始化完成");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"子系统初始化时发生异常");
    }
}

VoidResult UIEngine::ShutdownSubsystems(bool forceShutdown) {
    try {
        HHBUI_LOG_DEBUG(L"开始关闭子系统...");

        // 清理图标
        if (UIWinApi::ToList.hIcon) {
            DestroyIcon(UIWinApi::ToList.hIcon);
            UIWinApi::ToList.hIcon = nullptr;
        }
        if (UIWinApi::ToList.hIconsm) {
            DestroyIcon(UIWinApi::ToList.hIconsm);
            UIWinApi::ToList.hIconsm = nullptr;
        }

        // 关闭线程系统
        threadManager_.reset();

        // 关闭插件系统
        pluginManager_.reset();

        // 关闭资源管理器
        resourceManager_.reset();

        // 关闭字体管理器
        if (fontManager_) {
            fontManager_->Shutdown();
            fontManager_.reset();
        }
        if (UIWinApi::ToList.default_font) {
            delete UIWinApi::ToList.default_font;
            UIWinApi::ToList.default_font = nullptr;
        }

        // 关闭DPI管理器
        if (dpiManager_) {
            dpiManager_->Shutdown();
            dpiManager_.reset();
        }

        // 关闭渲染系统
        renderManager_.reset();
        UIDrawContext::UnInit();

        // 关闭WinAPI子系统
        UIWinApi::UnInit();
        UIWinApi::ToList.engine_instance = nullptr;

        // 关闭性能分析器
        profiler_.reset();

        // 关闭日志系统
        logger_.reset();

        HHBUI_LOG_DEBUG(L"子系统关闭完成");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"子系统关闭时发生异常");
    }
}

VoidResult UIEngine::InitializeRenderSystem(const RenderConfig& config) {
    try {
        if (FAILED(UIDrawContext::Init(config.deviceIndex))) {
            return VoidResult(E_FAIL, L"渲染上下文初始化失败");
        }

        HHBUI_LOG_DEBUG(L"渲染系统初始化成功");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"渲染系统初始化时发生异常");
    }
}

VoidResult UIEngine::InitializeFontSystem(const FontConfig& config) {
    try {
        // 获取系统默认字体
        UIWinApi::ToList.drawing_default_fontLogFont = new LOGFONTW();
        if (!SystemParametersInfoW(SPI_GETICONTITLELOGFONT, sizeof(LOGFONTW),
                                   UIWinApi::ToList.drawing_default_fontLogFont, FALSE)) {
            delete UIWinApi::ToList.drawing_default_fontLogFont;
            return VoidResult(E_FAIL, L"获取系统默认字体失败");
        }

        // 应用字体配置
        if (!config.faceName.empty()) {
            size_t len = std::min(config.faceName.length(), static_cast<size_t>(LF_FACESIZE - 1));
            wcsncpy_s(UIWinApi::ToList.drawing_default_fontLogFont->lfFaceName,
                     LF_FACESIZE, config.faceName.c_str(), len);
        }

        // 计算DPI缩放后的字体大小
        auto dpiInfo = GetSystemDPIInfo(config_.get() ? config_.get()->dpiConfig.customScale : 0.0f);
        UIWinApi::ToList.drawing_default_dpi = dpiInfo.scaleX;

        int scaledSize = static_cast<int>(std::round(config.size * dpiInfo.scaleX));
        UIWinApi::ToList.drawing_default_fontLogFont->lfHeight = -scaledSize;

        // 应用字体样式
        if (config.style != 0) {
            UIWinApi::ToList.drawing_default_fontLogFont->lfWeight =
                ((config.style & 1) == 0 ? FW_NORMAL : FW_BOLD); // Bold
            UIWinApi::ToList.drawing_default_fontLogFont->lfItalic =
                ((config.style & 2) == 0 ? 0 : 1); // Italic
            UIWinApi::ToList.drawing_default_fontLogFont->lfUnderline =
                ((config.style & 4) == 0 ? 0 : 1); // Underline
            UIWinApi::ToList.drawing_default_fontLogFont->lfStrikeOut =
                ((config.style & 8) == 0 ? 0 : 1); // Strikeout
        }

        // 创建默认字体
        UIWinApi::ToList.default_font = new UIFont();

        HHBUI_LOG_DEBUG(L"字体系统初始化成功");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"字体系统初始化时发生异常");
    }
}

VoidResult UIEngine::InitializeThreadSystem(const ThreadConfig& config) {
    try {
        // 这里可以初始化线程管理器
        // threadManager_ = std::make_shared<UIThreadManager>(config);

        HHBUI_LOG_DEBUG(L"线程系统初始化成功");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"线程系统初始化时发生异常");
    }
}

VoidResult UIEngine::ApplyConfig(const EngineInitConfig& config) {
    try {
        // 应用调试配置
        UIWinApi::ToList.dwDebug = config.debugConfig.enableDebugMode;

        // 应用DPI配置
        auto dpiInfo = GetSystemDPIInfo(config.dpiConfig.customScale);
        UIWinApi::ToList.CapsdpiX = static_cast<FLOAT>(dpiInfo.dpiX);
        UIWinApi::ToList.CapsdpiY = static_cast<FLOAT>(dpiInfo.dpiY);
        UIWinApi::ToList.drawing_default_dpi = dpiInfo.scaleX;

        HHBUI_LOG_DEBUG(L"配置应用完成");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"配置应用时发生异常");
    }
}

// ==================== DPI和缩放管理实现 ====================

float UIEngine::CalculateScaledValue(float value) const noexcept {
    try {
        if (dpiManager_) {
            return dpiManager_->CalculateScaledValue(value);
        }

        // 回退到系统DPI
        auto dpiInfo = GetSystemDPIInfo(config_ ? config_->dpiConfig.customScale : 0.0f);
        return value * dpiInfo.scaleX;

    } catch (...) {
        return value; // 异常时返回原值
    }
}

float UIEngine::GetDPIScale() const noexcept {
    try {
        if (dpiManager_) {
            return dpiManager_->GetSystemDPI().scaleX;
        }

        // 回退到系统DPI
        auto dpiInfo = GetSystemDPIInfo(config_ ? config_->dpiConfig.customScale : 0.0f);
        return dpiInfo.scaleX;

    } catch (...) {
        return 1.0f; // 异常时返回默认缩放
    }
}

float UIEngine::GetDPIScale(HMONITOR monitor) const noexcept {
    try {
        if (dpiManager_) {
            return dpiManager_->GetMonitorDPI(monitor).scaleX;
        }

        // 回退到系统实现
        if (!monitor) {
            return GetDPIScale();
        }

        UINT dpiX = USER_DEFAULT_SCREEN_DPI, dpiY = USER_DEFAULT_SCREEN_DPI;

        typedef HRESULT(WINAPI* GetDpiForMonitorProc)(HMONITOR, int, UINT*, UINT*);
        HMODULE shcore = LoadLibraryW(L"Shcore.dll");
        if (shcore) {
            auto getDpiForMonitor = reinterpret_cast<GetDpiForMonitorProc>(
                GetProcAddress(shcore, "GetDpiForMonitor"));
            if (getDpiForMonitor) {
                getDpiForMonitor(monitor, 0, &dpiX, &dpiY);
            }
            FreeLibrary(shcore);
        }

        return static_cast<float>(dpiX) / USER_DEFAULT_SCREEN_DPI;

    } catch (...) {
        return 1.0f;
    }
}

VoidResult UIEngine::SetCustomDPIScale(float scale) {
    try {
        if (!dpiManager_) {
            return VoidResult(E_FAIL, L"DPI管理器未初始化");
        }

        if (scale <= 0.0f || scale > 10.0f) {
            return VoidResult(E_INVALIDARG, L"DPI缩放值超出有效范围 (0.0-10.0)");
        }

        // 更新配置
        {
            std::unique_lock<std::shared_mutex> lock(configMutex_);
            if (config_) {
                config_->dpiConfig.customScale = scale;
            }
        }

        // 更新DPI管理器配置
        DPIConfig dpiConfig;
        dpiConfig.customScale = scale;
        return dpiManager_->UpdateConfig(dpiConfig);

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"设置DPI缩放时发生异常");
    }
}

VoidResult UIEngine::ResetDPIScale() {
    try {
        if (!dpiManager_) {
            return VoidResult(E_FAIL, L"DPI管理器未初始化");
        }

        // 重置配置
        {
            std::unique_lock<std::shared_mutex> lock(configMutex_);
            if (config_) {
                config_->dpiConfig.customScale = 0.0f;
            }
        }

        // 更新DPI管理器配置
        DPIConfig dpiConfig;
        dpiConfig.customScale = 0.0f;
        return dpiManager_->UpdateConfig(dpiConfig);

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"重置DPI缩放时发生异常");
    }
}

// ==================== 字体管理实现 ====================

std::shared_ptr<UIFont> UIEngine::GetDefaultFont() const noexcept {
    try {
        if (fontManager_) {
            return fontManager_->GetDefaultFont();
        }

        // 回退到全局默认字体
        return UIWinApi::ToList.default_font ?
               std::shared_ptr<UIFont>(UIWinApi::ToList.default_font, [](UIFont*){}) : nullptr;

    } catch (...) {
        return nullptr;
    }
}

VoidResult UIEngine::SetDefaultFont(const FontConfig& fontConfig) {
    try {
        if (!fontManager_) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }

        auto result = fontManager_->SetDefaultFont(fontConfig);
        if (result.IsFailure()) {
            return result;
        }

        // 更新配置
        {
            std::unique_lock<std::shared_mutex> lock(configMutex_);
            if (config_) {
                config_->fontConfig = fontConfig;
            }
        }

        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"设置默认字体时发生异常");
    }
}

Result<std::shared_ptr<UIFont>> UIEngine::CreateFont(const FontConfig& fontConfig) {
    try {
        if (!fontManager_) {
            return Result<std::shared_ptr<UIFont>>(E_FAIL, L"字体管理器未初始化");
        }

        float dpiScale = GetDPIScale();
        return fontManager_->CreateFont(fontConfig, dpiScale);

    } catch (...) {
        return Result<std::shared_ptr<UIFont>>(E_UNEXPECTED, L"创建字体时发生异常");
    }
}

VoidResult UIEngine::LoadFontFromMemory(const void* fontData, size_t dataSize, const std::wstring& fontFace) {
    try {
        if (!fontManager_) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }

        return fontManager_->LoadFontFromMemory(fontData, dataSize, fontFace);

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"从内存加载字体时发生异常");
    }
}

// ==================== 状态管理实现 ====================

void UIEngine::SetState(EngineState newState) {
    EngineState oldState = state_.exchange(newState);
    if (oldState != newState) {
        NotifyStateChange(oldState, newState);
    }
}

void UIEngine::NotifyStateChange(EngineState oldState, EngineState newState) {
    try {
        std::shared_lock<std::shared_mutex> lock(callbackMutex_);
        for (const auto& [id, callback] : stateCallbacks_) {
            try {
                callback(oldState, newState);
            } catch (...) {
                // 忽略回调异常
            }
        }
    } catch (...) {
        // 忽略通知异常
    }
}

void UIEngine::NotifyError(HRESULT errorCode, const std::wstring& message, const std::wstring& details) {
    try {
        std::shared_lock<std::shared_mutex> lock(callbackMutex_);
        for (const auto& [id, callback] : errorCallbacks_) {
            try {
                callback(errorCode, message, details);
            } catch (...) {
                // 忽略回调异常
            }
        }
    } catch (...) {
        // 忽略通知异常
    }
}

uint64_t UIEngine::GetNextCallbackId() {
    return nextCallbackId_.fetch_add(1, std::memory_order_relaxed);
}

// ==================== 回调管理实现 ====================

uint64_t UIEngine::RegisterStateCallback(const EngineStateCallback& callback) {
    try {
        uint64_t id = GetNextCallbackId();
        std::unique_lock<std::shared_mutex> lock(callbackMutex_);
        stateCallbacks_[id] = callback;
        return id;
    } catch (...) {
        return 0; // 失败返回0
    }
}

uint64_t UIEngine::RegisterErrorCallback(const ErrorCallback& callback) {
    try {
        uint64_t id = GetNextCallbackId();
        std::unique_lock<std::shared_mutex> lock(callbackMutex_);
        errorCallbacks_[id] = callback;
        return id;
    } catch (...) {
        return 0;
    }
}

uint64_t UIEngine::RegisterPerformanceCallback(const PerformanceCallback& callback) {
    try {
        uint64_t id = GetNextCallbackId();
        std::unique_lock<std::shared_mutex> lock(callbackMutex_);
        performanceCallbacks_[id] = callback;
        return id;
    } catch (...) {
        return 0;
    }
}

bool UIEngine::UnregisterCallback(uint64_t callbackId) {
    try {
        std::unique_lock<std::shared_mutex> lock(callbackMutex_);

        auto stateIt = stateCallbacks_.find(callbackId);
        if (stateIt != stateCallbacks_.end()) {
            stateCallbacks_.erase(stateIt);
            return true;
        }

        auto errorIt = errorCallbacks_.find(callbackId);
        if (errorIt != errorCallbacks_.end()) {
            errorCallbacks_.erase(errorIt);
            return true;
        }

        auto perfIt = performanceCallbacks_.find(callbackId);
        if (perfIt != performanceCallbacks_.end()) {
            performanceCallbacks_.erase(perfIt);
            return true;
        }

        return false;

    } catch (...) {
        return false;
    }
}

} // namespace HHBUI