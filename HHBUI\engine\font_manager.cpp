/**
 * @file font_manager.cpp
 * @brief 字体管理器实现
 * <AUTHOR> Team
 * @version 2.0.0
 * @date 2025-01-30
 */

#include "pch.h"
#include "font_manager.h"
#include "engine.h"
#include "element/font.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <filesystem>

namespace HHBUI {

// ==================== UIFontManager实现 ====================

UIFontManager::UIFontManager()
    : maxCacheSize_(64 * 1024 * 1024) // 64MB
    , maxCacheCount_(1000)
    , currentCacheSize_(0)
    , cacheHits_(0)
    , cacheMisses_(0)
    , totalMemoryUsage_(0)
    , nextCallbackId_(1)
    , initialized_(false)
    , lastCleanup_(std::chrono::steady_clock::now())
    , cleanupInterval_(std::chrono::minutes(5)) {
}

UIFontManager::~UIFontManager() {
    Shutdown();
}

VoidResult UIFontManager::Initialize(const FontConfig& config) {
    try {
        if (initialized_.load()) {
            return VoidResult(E_FAIL, L"字体管理器已经初始化");
        }
        
        // 保存配置
        {
            std::lock_guard<std::mutex> lock(configMutex_);
            config_ = config;
        }
        
        // 创建默认字体
        auto defaultFontResult = CreateFont(config);
        if (defaultFontResult.IsFailure()) {
            return VoidResult(defaultFontResult.GetErrorCode(), 
                            L"创建默认字体失败: " + defaultFontResult.GetErrorMessage());
        }
        
        {
            std::lock_guard<std::mutex> lock(defaultFontMutex_);
            defaultFont_ = defaultFontResult.GetValue();
        }
        
        initialized_.store(true);
        HHBUI_LOG_INFO(L"字体管理器初始化成功");
        
        return VoidResult();
        
    } catch (const std::exception& e) {
        std::wstring message = L"字体管理器初始化异常: ";
        int size = MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, nullptr, 0);
        if (size > 0) {
            std::wstring wstr(size - 1, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, &wstr[0], size);
            message += wstr;
        }
        return VoidResult(E_UNEXPECTED, message);
    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"字体管理器初始化未知异常");
    }
}

void UIFontManager::Shutdown() {
    try {
        if (!initialized_.load()) {
            return;
        }
        
        // 清理回调
        {
            std::lock_guard<std::mutex> lock(callbackMutex_);
            loadCallbacks_.clear();
            cleanupCallbacks_.clear();
        }
        
        // 清理缓存
        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            fontCache_.clear();
            currentCacheSize_.store(0);
        }
        
        // 清理已加载字体
        {
            std::lock_guard<std::mutex> lock(fontsMutex_);
            loadedFonts_.clear();
        }
        
        // 清理默认字体
        {
            std::lock_guard<std::mutex> lock(defaultFontMutex_);
            defaultFont_.reset();
        }
        
        // 重置统计
        cacheHits_.store(0);
        cacheMisses_.store(0);
        totalMemoryUsage_.store(0);
        
        initialized_.store(false);
        HHBUI_LOG_INFO(L"字体管理器已关闭");
        
    } catch (...) {
        HHBUI_LOG_WARNING(L"字体管理器关闭时发生异常");
    }
}

VoidResult UIFontManager::UpdateConfig(const FontConfig& config) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }
        
        {
            std::lock_guard<std::mutex> lock(configMutex_);
            config_ = config;
        }
        
        // 更新默认字体
        auto defaultFontResult = CreateFont(config);
        if (defaultFontResult.IsSuccess()) {
            std::lock_guard<std::mutex> lock(defaultFontMutex_);
            defaultFont_ = defaultFontResult.GetValue();
        }
        
        HHBUI_LOG_DEBUG(L"字体配置已更新");
        return VoidResult();
        
    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"字体配置更新时发生异常");
    }
}

Result<std::shared_ptr<UIFont>> UIFontManager::CreateFont(const FontConfig& config, float dpiScale) {
    try {
        if (!initialized_.load()) {
            return Result<std::shared_ptr<UIFont>>(E_FAIL, L"字体管理器未初始化");
        }
        
        // 如果没有指定DPI缩放，使用系统DPI
        if (dpiScale <= 0.0f) {
            auto engine = UIEngine::GetInstance();
            if (engine) {
                dpiScale = engine->GetDPIScale();
            } else {
                dpiScale = 1.0f;
            }
        }
        
        // 创建缓存键
        FontCacheKey key = CreateCacheKey(config, dpiScale);
        
        // 尝试从缓存获取
        auto cachedFont = GetFromCache(key);
        if (cachedFont) {
            cacheHits_.fetch_add(1, std::memory_order_relaxed);
            return Result<std::shared_ptr<UIFont>>(cachedFont);
        }
        
        // 缓存未命中，创建新字体
        cacheMisses_.fetch_add(1, std::memory_order_relaxed);
        
        auto font = CreateFontInternal(config, dpiScale);
        if (!font) {
            return Result<std::shared_ptr<UIFont>>(E_FAIL, L"字体创建失败");
        }
        
        // 计算内存使用并添加到缓存
        size_t memoryUsage = CalculateFontMemoryUsage(font);
        AddToCache(key, font, memoryUsage);
        
        return Result<std::shared_ptr<UIFont>>(font);
        
    } catch (const std::exception& e) {
        std::wstring message = L"字体创建异常: ";
        int size = MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, nullptr, 0);
        if (size > 0) {
            std::wstring wstr(size - 1, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, &wstr[0], size);
            message += wstr;
        }
        return Result<std::shared_ptr<UIFont>>(E_UNEXPECTED, message);
    } catch (...) {
        return Result<std::shared_ptr<UIFont>>(E_UNEXPECTED, L"字体创建未知异常");
    }
}

std::shared_ptr<UIFont> UIFontManager::GetDefaultFont() const {
    try {
        std::lock_guard<std::mutex> lock(defaultFontMutex_);
        return defaultFont_;
    } catch (...) {
        return nullptr;
    }
}

VoidResult UIFontManager::SetDefaultFont(const FontConfig& config) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }
        
        auto fontResult = CreateFont(config);
        if (fontResult.IsFailure()) {
            return VoidResult(fontResult.GetErrorCode(), 
                            L"设置默认字体失败: " + fontResult.GetErrorMessage());
        }
        
        {
            std::lock_guard<std::mutex> lock(defaultFontMutex_);
            defaultFont_ = fontResult.GetValue();
        }
        
        // 更新配置
        {
            std::lock_guard<std::mutex> lock(configMutex_);
            config_ = config;
        }
        
        HHBUI_LOG_DEBUG(L"默认字体已设置");
        return VoidResult();
        
    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"设置默认字体时发生异常");
    }
}

VoidResult UIFontManager::PreloadFonts(const std::vector<FontConfig>& configs) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }
        
        size_t successCount = 0;
        size_t failureCount = 0;
        
        for (const auto& config : configs) {
            auto result = CreateFont(config);
            if (result.IsSuccess()) {
                successCount++;
            } else {
                failureCount++;
                HHBUI_LOG_WARNING(L"预加载字体失败: " + config.faceName + 
                                 L", 错误: " + result.GetErrorMessage());
            }
        }
        
        HHBUI_LOG_INFO(L"字体预加载完成: 成功 " + std::to_wstring(successCount) + 
                      L", 失败 " + std::to_wstring(failureCount));
        
        return VoidResult();
        
    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"字体预加载时发生异常");
    }
}

VoidResult UIFontManager::LoadFontFromFile(const std::wstring& filePath) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }
        
        // 检查文件是否存在
        if (!std::filesystem::exists(filePath)) {
            return VoidResult(E_INVALIDARG, L"字体文件不存在: " + filePath);
        }
        
        // 读取文件数据
        std::ifstream file(filePath, std::ios::binary | std::ios::ate);
        if (!file.is_open()) {
            return VoidResult(E_FAIL, L"无法打开字体文件: " + filePath);
        }
        
        size_t fileSize = static_cast<size_t>(file.tellg());
        file.seekg(0, std::ios::beg);
        
        std::vector<uint8_t> fontData(fileSize);
        if (!file.read(reinterpret_cast<char*>(fontData.data()), fileSize)) {
            return VoidResult(E_FAIL, L"读取字体文件失败: " + filePath);
        }
        
        // 提取字体名称（简化实现）
        std::wstring fontFace = std::filesystem::path(filePath).stem().wstring();
        
        // 加载字体
        auto result = LoadFontFromMemory(fontData.data(), fontData.size(), fontFace);
        if (result.IsFailure()) {
            return result;
        }
        
        // 保存字体文件信息
        {
            std::lock_guard<std::mutex> lock(fontsMutex_);
            FontFileInfo info;
            info.filePath = filePath;
            info.fontData = std::move(fontData);
            info.fontFaces.push_back(fontFace);
            info.isMemoryFont = false;
            info.loadTime = std::chrono::steady_clock::now();
            
            loadedFonts_[fontFace] = std::move(info);
        }
        
        NotifyFontLoad(fontFace, true);
        HHBUI_LOG_DEBUG(L"字体文件加载成功: " + filePath);
        
        return VoidResult();
        
    } catch (const std::exception& e) {
        std::wstring message = L"字体文件加载异常: ";
        int size = MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, nullptr, 0);
        if (size > 0) {
            std::wstring wstr(size - 1, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, &wstr[0], size);
            message += wstr;
        }
        NotifyFontLoad(filePath, false);
        return VoidResult(E_UNEXPECTED, message);
    } catch (...) {
        NotifyFontLoad(filePath, false);
        return VoidResult(E_UNEXPECTED, L"字体文件加载未知异常");
    }
}

VoidResult UIFontManager::LoadFontFromMemory(const void* fontData, size_t dataSize, const std::wstring& fontFace) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }
        
        if (!fontData || dataSize == 0) {
            return VoidResult(E_INVALIDARG, L"字体数据无效");
        }
        
        if (fontFace.empty()) {
            return VoidResult(E_INVALIDARG, L"字体名称不能为空");
        }
        
        // 使用UIFont的静态方法加载字体
        if (!UIFont::LoadFromMem(const_cast<void*>(fontData), dataSize, fontFace.c_str())) {
            return VoidResult(E_FAIL, L"字体数据加载失败: " + fontFace);
        }
        
        // 保存字体信息
        {
            std::lock_guard<std::mutex> lock(fontsMutex_);
            FontFileInfo info;
            info.fontData.assign(static_cast<const uint8_t*>(fontData), 
                               static_cast<const uint8_t*>(fontData) + dataSize);
            info.fontFaces.push_back(fontFace);
            info.isMemoryFont = true;
            info.loadTime = std::chrono::steady_clock::now();
            
            loadedFonts_[fontFace] = std::move(info);
        }
        
        NotifyFontLoad(fontFace, true);
        HHBUI_LOG_DEBUG(L"内存字体加载成功: " + fontFace);
        
        return VoidResult();
        
    } catch (const std::exception& e) {
        std::wstring message = L"内存字体加载异常: ";
        int size = MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, nullptr, 0);
        if (size > 0) {
            std::wstring wstr(size - 1, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, &wstr[0], size);
            message += wstr;
        }
        NotifyFontLoad(fontFace, false);
        return VoidResult(E_UNEXPECTED, message);
    } catch (...) {
        NotifyFontLoad(fontFace, false);
        return VoidResult(E_UNEXPECTED, L"内存字体加载未知异常");
    }
}

VoidResult UIFontManager::UnloadFont(const std::wstring& fontFace) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }

        // 从已加载字体中移除
        {
            std::lock_guard<std::mutex> lock(fontsMutex_);
            auto it = loadedFonts_.find(fontFace);
            if (it == loadedFonts_.end()) {
                return VoidResult(E_INVALIDARG, L"字体未加载: " + fontFace);
            }
            loadedFonts_.erase(it);
        }

        // 从缓存中移除相关字体
        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            auto it = fontCache_.begin();
            while (it != fontCache_.end()) {
                if (it->first.faceName == fontFace) {
                    currentCacheSize_.fetch_sub(it->second.memoryUsage, std::memory_order_relaxed);
                    it = fontCache_.erase(it);
                } else {
                    ++it;
                }
            }
        }

        HHBUI_LOG_DEBUG(L"字体已卸载: " + fontFace);
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"字体卸载时发生异常");
    }
}

std::vector<std::wstring> UIFontManager::GetLoadedFonts() const {
    try {
        std::lock_guard<std::mutex> lock(fontsMutex_);
        std::vector<std::wstring> fonts;
        fonts.reserve(loadedFonts_.size());

        for (const auto& [fontFace, info] : loadedFonts_) {
            fonts.push_back(fontFace);
        }

        return fonts;

    } catch (...) {
        return {};
    }
}

bool UIFontManager::IsFontAvailable(const std::wstring& fontFace) const {
    try {
        // 检查已加载字体
        {
            std::lock_guard<std::mutex> lock(fontsMutex_);
            if (loadedFonts_.find(fontFace) != loadedFonts_.end()) {
                return true;
            }
        }

        // 检查系统字体（简化实现）
        HDC hdc = GetDC(nullptr);
        if (!hdc) return false;

        LOGFONTW logFont = {};
        wcscpy_s(logFont.lfFaceName, LF_FACESIZE, fontFace.c_str());
        logFont.lfCharSet = DEFAULT_CHARSET;

        bool found = false;
        EnumFontFamiliesExW(hdc, &logFont, [](const LOGFONTW* lpelfe, const TEXTMETRICW* lpntme,
                                             DWORD FontType, LPARAM lParam) -> int {
            bool* pFound = reinterpret_cast<bool*>(lParam);
            *pFound = true;
            return 0; // 停止枚举
        }, reinterpret_cast<LPARAM>(&found), 0);

        ReleaseDC(nullptr, hdc);
        return found;

    } catch (...) {
        return false;
    }
}

VoidResult UIFontManager::CleanupCache(bool forceCleanup) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }

        auto now = std::chrono::steady_clock::now();

        // 检查是否需要清理
        if (!forceCleanup) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastCleanup_);
            if (elapsed < cleanupInterval_) {
                return VoidResult(); // 还不需要清理
            }
        }

        size_t removedCount = PerformCacheCleanup(forceCleanup);
        lastCleanup_ = now;

        HHBUI_LOG_DEBUG(L"缓存清理完成，移除 " + std::to_wstring(removedCount) + L" 个字体");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"缓存清理时发生异常");
    }
}

void UIFontManager::SetCacheLimits(size_t maxCacheSize, size_t maxCacheCount) {
    maxCacheSize_.store(maxCacheSize);
    maxCacheCount_.store(maxCacheCount);

    // 立即检查限制
    CheckCacheLimits();

    HHBUI_LOG_DEBUG(L"缓存限制已更新: 大小=" + std::to_wstring(maxCacheSize) +
                    L", 数量=" + std::to_wstring(maxCacheCount));
}

VoidResult UIFontManager::WarmupCache(const std::vector<FontConfig>& commonConfigs) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"字体管理器未初始化");
        }

        HHBUI_LOG_DEBUG(L"开始缓存预热...");

        size_t successCount = 0;
        for (const auto& config : commonConfigs) {
            auto result = CreateFont(config);
            if (result.IsSuccess()) {
                successCount++;
            }
        }

        HHBUI_LOG_INFO(L"缓存预热完成: " + std::to_wstring(successCount) + L"/" +
                      std::to_wstring(commonConfigs.size()) + L" 个字体");

        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"缓存预热时发生异常");
    }
}

std::unordered_map<std::string, size_t> UIFontManager::GetCacheStats() const {
    try {
        std::unordered_map<std::string, size_t> stats;

        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            stats["cache_size"] = fontCache_.size();
        }

        stats["max_cache_size"] = maxCacheSize_.load();
        stats["max_cache_count"] = maxCacheCount_.load();
        stats["current_memory_usage"] = currentCacheSize_.load();
        stats["total_memory_usage"] = totalMemoryUsage_.load();
        stats["cache_hits"] = cacheHits_.load();
        stats["cache_misses"] = cacheMisses_.load();

        size_t totalAccess = stats["cache_hits"] + stats["cache_misses"];
        stats["hit_ratio_percent"] = totalAccess > 0 ? (stats["cache_hits"] * 100 / totalAccess) : 0;

        {
            std::lock_guard<std::mutex> lock(fontsMutex_);
            stats["loaded_fonts"] = loadedFonts_.size();
        }

        return stats;

    } catch (...) {
        return {};
    }
}

std::vector<std::wstring> UIFontManager::EnumerateSystemFonts() const {
    try {
        std::vector<std::wstring> fonts;

        HDC hdc = GetDC(nullptr);
        if (!hdc) return fonts;

        LOGFONTW logFont = {};
        logFont.lfCharSet = DEFAULT_CHARSET;

        EnumFontFamiliesExW(hdc, &logFont, [](const LOGFONTW* lpelfe, const TEXTMETRICW* lpntme,
                                             DWORD FontType, LPARAM lParam) -> int {
            auto* pFonts = reinterpret_cast<std::vector<std::wstring>*>(lParam);
            std::wstring fontName = lpelfe->lfFaceName;

            // 避免重复
            if (std::find(pFonts->begin(), pFonts->end(), fontName) == pFonts->end()) {
                pFonts->push_back(fontName);
            }

            return 1; // 继续枚举
        }, reinterpret_cast<LPARAM>(&fonts), 0);

        ReleaseDC(nullptr, hdc);

        // 排序
        std::sort(fonts.begin(), fonts.end());

        return fonts;

    } catch (...) {
        return {};
    }
}

FontStatistics UIFontManager::GetStatistics() const {
    try {
        FontStatistics stats;

        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            stats.cachedFonts = fontCache_.size();
        }

        {
            std::lock_guard<std::mutex> lock(fontsMutex_);
            stats.totalFonts = loadedFonts_.size();
            stats.memoryFonts = std::count_if(loadedFonts_.begin(), loadedFonts_.end(),
                                            [](const auto& pair) { return pair.second.isMemoryFont; });
        }

        stats.totalMemoryUsage = totalMemoryUsage_.load();
        stats.cacheHits = cacheHits_.load();
        stats.cacheMisses = cacheMisses_.load();

        size_t totalAccess = stats.cacheHits + stats.cacheMisses;
        stats.hitRatio = totalAccess > 0 ? static_cast<double>(stats.cacheHits) / totalAccess : 0.0;

        return stats;

    } catch (...) {
        return FontStatistics();
    }
}

void UIFontManager::ResetStatistics() {
    cacheHits_.store(0);
    cacheMisses_.store(0);
    totalMemoryUsage_.store(currentCacheSize_.load());

    HHBUI_LOG_DEBUG(L"字体统计信息已重置");
}

// ==================== 私有方法实现 ====================

FontCacheKey UIFontManager::CreateCacheKey(const FontConfig& config, float dpiScale) const {
    return FontCacheKey(config.faceName, config.size, config.style, dpiScale);
}

std::shared_ptr<UIFont> UIFontManager::CreateFontInternal(const FontConfig& config, float dpiScale) {
    try {
        // 计算DPI缩放后的字体大小
        int32_t scaledSize = static_cast<int32_t>(std::round(config.size * dpiScale));

        // 创建字体
        auto font = std::make_shared<UIFont>(config.faceName.c_str(), scaledSize, config.style);

        return font;

    } catch (...) {
        return nullptr;
    }
}

void UIFontManager::AddToCache(const FontCacheKey& key, std::shared_ptr<UIFont> font, size_t memoryUsage) {
    try {
        std::lock_guard<std::mutex> lock(cacheMutex_);

        FontCacheItem item(font, memoryUsage);
        fontCache_[key] = item;

        currentCacheSize_.fetch_add(memoryUsage, std::memory_order_relaxed);
        totalMemoryUsage_.fetch_add(memoryUsage, std::memory_order_relaxed);

        // 检查缓存限制
        CheckCacheLimits();

    } catch (...) {
        // 忽略缓存添加异常
    }
}

std::shared_ptr<UIFont> UIFontManager::GetFromCache(const FontCacheKey& key) {
    try {
        std::lock_guard<std::mutex> lock(cacheMutex_);

        auto it = fontCache_.find(key);
        if (it != fontCache_.end()) {
            // 更新访问信息
            it->second.lastAccess = std::chrono::steady_clock::now();
            it->second.accessCount++;

            return it->second.font;
        }

        return nullptr;

    } catch (...) {
        return nullptr;
    }
}

void UIFontManager::CheckCacheLimits() {
    // 注意：此方法假设已经持有cacheMutex_锁

    size_t currentSize = currentCacheSize_.load();
    size_t currentCount = fontCache_.size();

    if (currentSize > maxCacheSize_.load() || currentCount > maxCacheCount_.load()) {
        PerformCacheCleanup(false);
    }
}

size_t UIFontManager::PerformCacheCleanup(bool forceCleanup) {
    try {
        std::lock_guard<std::mutex> lock(cacheMutex_);

        if (fontCache_.empty()) {
            return 0;
        }

        // 收集清理候选项
        std::vector<std::pair<FontCacheKey, FontCacheItem*>> candidates;
        candidates.reserve(fontCache_.size());

        auto now = std::chrono::steady_clock::now();

        for (auto& [key, item] : fontCache_) {
            // 检查是否应该清理
            bool shouldCleanup = forceCleanup;

            if (!shouldCleanup) {
                // 检查最后访问时间
                auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - item.lastAccess);
                if (elapsed.count() > 30) { // 30分钟未访问
                    shouldCleanup = true;
                }

                // 检查访问频率
                if (item.accessCount < 2) { // 访问次数少
                    shouldCleanup = true;
                }
            }

            if (shouldCleanup) {
                candidates.emplace_back(key, &item);
            }
        }

        // 按访问时间排序（最久未访问的优先清理）
        std::sort(candidates.begin(), candidates.end(),
                 [](const auto& a, const auto& b) {
                     return a.second->lastAccess < b.second->lastAccess;
                 });

        // 执行清理
        size_t removedCount = 0;
        size_t freedMemory = 0;

        for (const auto& [key, item] : candidates) {
            freedMemory += item->memoryUsage;
            fontCache_.erase(key);
            removedCount++;

            // 如果不是强制清理，检查是否已满足条件
            if (!forceCleanup) {
                size_t newSize = currentCacheSize_.load() - freedMemory;
                size_t newCount = fontCache_.size();

                if (newSize <= maxCacheSize_.load() && newCount <= maxCacheCount_.load()) {
                    break;
                }
            }
        }

        currentCacheSize_.fetch_sub(freedMemory, std::memory_order_relaxed);

        if (removedCount > 0) {
            NotifyCleanup(removedCount, freedMemory);
        }

        return removedCount;

    } catch (...) {
        return 0;
    }
}

size_t UIFontManager::CalculateFontMemoryUsage(const std::shared_ptr<UIFont>& font) const {
    // 简化的内存使用计算
    // 实际实现应该根据字体的具体信息计算
    return 1024; // 假设每个字体占用1KB
}

void UIFontManager::NotifyFontLoad(const std::wstring& fontFace, bool success) {
    try {
        std::lock_guard<std::mutex> lock(callbackMutex_);
        for (const auto& [id, callback] : loadCallbacks_) {
            try {
                callback(fontFace, success);
            } catch (...) {
                // 忽略回调异常
            }
        }
    } catch (...) {
        // 忽略通知异常
    }
}

void UIFontManager::NotifyCleanup(size_t removedCount, size_t freedMemory) {
    try {
        std::lock_guard<std::mutex> lock(callbackMutex_);
        for (const auto& [id, callback] : cleanupCallbacks_) {
            try {
                callback(removedCount, freedMemory);
            } catch (...) {
                // 忽略回调异常
            }
        }
    } catch (...) {
        // 忽略通知异常
    }
}

uint64_t UIFontManager::GetNextCallbackId() {
    return nextCallbackId_.fetch_add(1, std::memory_order_relaxed);
}

} // namespace HHBUI
