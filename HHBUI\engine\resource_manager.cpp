/**
 * @file resource_manager.cpp
 * @brief 资源管理器实现
 * <AUTHOR> Team
 * @version 2.0.0
 * @date 2025-01-30
 */

#include "pch.h"
#include "resource_manager.h"
#include "engine.h"
#include <algorithm>
#include <sstream>
#include <iomanip>

namespace HHBUI {

// ==================== MemoryPool实现 ====================

MemoryPool::MemoryPool(MemoryPoolType type, size_t blockSize, size_t initialBlocks)
    : type_(type)
    , blockSize_(blockSize)
    , totalAllocated_(0)
    , totalUsed_(0)
    , allocationCount_(0)
    , deallocationCount_(0) {
    
    blocks_.reserve(initialBlocks * 2); // 预留空间
    
    // 初始化内存块
    for (size_t i = 0; i < initialBlocks; ++i) {
        void* ptr = std::aligned_alloc(32, blockSize_); // 32字节对齐
        if (ptr) {
            blocks_.emplace_back(ptr, blockSize_, type_);
            blocks_.back().inUse = false;
            freeBlocks_.push(blocks_.size() - 1);
            totalAllocated_.fetch_add(blockSize_, std::memory_order_relaxed);
        }
    }
}

MemoryPool::~MemoryPool() {
    for (auto& block : blocks_) {
        if (block.ptr) {
            std::free(block.ptr);
        }
    }
}

void* MemoryPool::Allocate(size_t size) {
    if (size > blockSize_) {
        return nullptr; // 超出块大小
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 查找空闲块
    if (freeBlocks_.empty()) {
        ExpandPool();
    }
    
    if (!freeBlocks_.empty()) {
        size_t index = freeBlocks_.front();
        freeBlocks_.pop();
        
        auto& block = blocks_[index];
        block.inUse = true;
        block.allocTime = std::chrono::steady_clock::now();
        
        totalUsed_.fetch_add(blockSize_, std::memory_order_relaxed);
        allocationCount_.fetch_add(1, std::memory_order_relaxed);
        
        return block.ptr;
    }
    
    return nullptr;
}

void MemoryPool::Deallocate(void* ptr) {
    if (!ptr) return;
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 查找对应的块
    for (size_t i = 0; i < blocks_.size(); ++i) {
        if (blocks_[i].ptr == ptr && blocks_[i].inUse) {
            blocks_[i].inUse = false;
            freeBlocks_.push(i);
            
            totalUsed_.fetch_sub(blockSize_, std::memory_order_relaxed);
            deallocationCount_.fetch_add(1, std::memory_order_relaxed);
            
            return;
        }
    }
}

MemoryStatistics MemoryPool::GetStatistics() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    MemoryStatistics stats;
    stats.totalAllocated = totalAllocated_.load();
    stats.totalUsed = totalUsed_.load();
    stats.totalFree = stats.totalAllocated - stats.totalUsed;
    stats.allocationCount = allocationCount_.load();
    stats.deallocationCount = deallocationCount_.load();
    
    // 计算碎片化率
    size_t usedBlocks = 0;
    for (const auto& block : blocks_) {
        if (block.inUse) usedBlocks++;
    }
    
    if (!blocks_.empty()) {
        stats.fragmentationRatio = (blocks_.size() - usedBlocks) * 100 / blocks_.size();
    }
    
    stats.poolUsage[type_] = stats.totalUsed;
    stats.poolCount[type_] = blocks_.size();
    
    return stats;
}

size_t MemoryPool::Cleanup() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    size_t cleanedCount = 0;
    auto now = std::chrono::steady_clock::now();
    
    // 清理长时间未使用的块（保留一定数量的空闲块）
    size_t minFreeBlocks = std::max(size_t(4), blocks_.size() / 4);
    
    if (freeBlocks_.size() > minFreeBlocks) {
        // 这里简化实现，实际应该根据时间戳清理
        size_t toClean = freeBlocks_.size() - minFreeBlocks;
        cleanedCount = std::min(toClean, freeBlocks_.size() / 2);
    }
    
    return cleanedCount;
}

void MemoryPool::ExpandPool() {
    // 扩展池大小（当前大小的50%）
    size_t expandCount = std::max(size_t(4), blocks_.size() / 2);
    
    for (size_t i = 0; i < expandCount; ++i) {
        void* ptr = std::aligned_alloc(32, blockSize_);
        if (ptr) {
            blocks_.emplace_back(ptr, blockSize_, type_);
            blocks_.back().inUse = false;
            freeBlocks_.push(blocks_.size() - 1);
            totalAllocated_.fetch_add(blockSize_, std::memory_order_relaxed);
        } else {
            break; // 内存不足
        }
    }
}

// ==================== UIResourceManager实现 ====================

UIResourceManager::UIResourceManager()
    : gcEnabled_(true)
    , autoGCRunning_(false)
    , leakDetectionEnabled_(false)
    , leakDetectionRunning_(false)
    , maxCacheSize_(64 * 1024 * 1024) // 64MB
    , maxCacheCount_(1000)
    , nextCallbackId_(1)
    , initialized_(false)
    , shutdownRequested_(false) {
}

UIResourceManager::~UIResourceManager() {
    Shutdown();
}

VoidResult UIResourceManager::Initialize(const PerformanceConfig& config) {
    try {
        if (initialized_.load()) {
            return VoidResult(E_FAIL, L"资源管理器已经初始化");
        }
        
        // 保存配置
        {
            std::lock_guard<std::mutex> lock(configMutex_);
            config_ = config;
        }
        
        // 初始化内存池
        memoryPools_.clear();
        
        // 小块内存池 (< 1KB)
        memoryPools_.push_back(std::make_unique<MemoryPool>(MemoryPoolType::Small, 1024, 64));
        
        // 中等内存池 (1KB - 64KB)
        memoryPools_.push_back(std::make_unique<MemoryPool>(MemoryPoolType::Medium, 64 * 1024, 32));
        
        // 大块内存池 (64KB - 1MB)
        memoryPools_.push_back(std::make_unique<MemoryPool>(MemoryPoolType::Large, 1024 * 1024, 16));
        
        // 巨大内存池 (> 1MB)
        memoryPools_.push_back(std::make_unique<MemoryPool>(MemoryPoolType::Huge, 4 * 1024 * 1024, 8));
        
        // 设置缓存限制
        maxCacheSize_.store(config.memoryPoolSize);
        maxCacheCount_.store(config.maxCachedObjects);
        
        // 启动自动垃圾回收
        if (config.gcInterval.count() > 0) {
            StartAutoGC(config.gcInterval);
        }
        
        initialized_.store(true);
        HHBUI_LOG_INFO(L"资源管理器初始化成功");
        
        return VoidResult();
        
    } catch (const std::exception& e) {
        std::wstring message = L"资源管理器初始化异常: ";
        int size = MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, nullptr, 0);
        if (size > 0) {
            std::wstring wstr(size - 1, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, e.what(), -1, &wstr[0], size);
            message += wstr;
        }
        return VoidResult(E_UNEXPECTED, message);
    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"资源管理器初始化未知异常");
    }
}

void UIResourceManager::Shutdown() {
    try {
        if (!initialized_.load()) {
            return;
        }
        
        shutdownRequested_.store(true);
        
        // 停止自动垃圾回收
        StopAutoGC();
        
        // 停止内存泄漏检测
        StopLeakDetection();
        
        // 清理所有资源
        {
            HHBUI_UNIQUE_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);
            resources_.clear();
            resourceInfos_.clear();
        }
        
        // 清理内存池
        {
            std::lock_guard<std::mutex> lock(memoryMutex_);
            allocations_.clear();
            memoryPools_.clear();
        }
        
        // 清理回调
        {
            std::lock_guard<std::mutex> lock(callbackMutex_);
            loadCallbacks_.clear();
            lowMemoryCallbacks_.clear();
            gcCallbacks_.clear();
        }
        
        initialized_.store(false);
        HHBUI_LOG_INFO(L"资源管理器已关闭");
        
    } catch (...) {
        HHBUI_LOG_WARNING(L"资源管理器关闭时发生异常");
    }
}

VoidResult UIResourceManager::UpdateConfig(const PerformanceConfig& config) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"资源管理器未初始化");
        }
        
        {
            std::lock_guard<std::mutex> lock(configMutex_);
            config_ = config;
        }
        
        // 更新缓存限制
        maxCacheSize_.store(config.memoryPoolSize);
        maxCacheCount_.store(config.maxCachedObjects);
        
        HHBUI_LOG_DEBUG(L"资源管理器配置已更新");
        return VoidResult();
        
    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"资源管理器配置更新时发生异常");
    }
}

VoidResult UIResourceManager::RegisterResource(std::shared_ptr<IResource> resource) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"资源管理器未初始化");
        }
        
        if (!resource) {
            return VoidResult(E_INVALIDARG, L"资源对象无效");
        }
        
        std::string resourceId = resource->GetId();
        if (resourceId.empty()) {
            return VoidResult(E_INVALIDARG, L"资源ID不能为空");
        }
        
        {
            HHBUI_UNIQUE_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);
            
            // 检查是否已存在
            if (resources_.find(resourceId) != resources_.end()) {
                return VoidResult(E_FAIL, L"资源已存在: " + std::wstring(resourceId.begin(), resourceId.end()));
            }
            
            // 注册资源
            resources_[resourceId] = resource;
            
            // 创建资源信息
            ResourceInfo info;
            info.id = resourceId;
            info.type = resource->GetType();
            info.state = ResourceState::Loaded;
            info.size = resource->GetSize();
            info.createTime = std::chrono::steady_clock::now();
            info.lastAccess = info.createTime;
            info.accessCount = 0;
            info.refCount = resource->GetRefCount();
            
            resourceInfos_[resourceId] = info;
        }
        
        NotifyResourceLoad(resourceId, true);
        HHBUI_LOG_DEBUG(L"资源已注册: " + std::wstring(resourceId.begin(), resourceId.end()));
        
        return VoidResult();
        
    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"资源注册时发生异常");
    }
}

std::shared_ptr<IResource> UIResourceManager::GetResource(const std::string& resourceId) {
    try {
        if (!initialized_.load()) {
            return nullptr;
        }
        
        HHBUI_SHARED_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);
        
        auto it = resources_.find(resourceId);
        if (it != resources_.end()) {
            // 更新访问信息
            auto infoIt = resourceInfos_.find(resourceId);
            if (infoIt != resourceInfos_.end()) {
                infoIt->second.lastAccess = std::chrono::steady_clock::now();
                infoIt->second.accessCount++;
            }
            
            return it->second;
        }
        
        return nullptr;
        
    } catch (...) {
        return nullptr;
    }
}

VoidResult UIResourceManager::RemoveResource(const std::string& resourceId) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"资源管理器未初始化");
        }

        {
            HHBUI_UNIQUE_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);

            auto it = resources_.find(resourceId);
            if (it == resources_.end()) {
                return VoidResult(E_INVALIDARG, L"资源不存在: " + std::wstring(resourceId.begin(), resourceId.end()));
            }

            // 移除资源
            resources_.erase(it);
            resourceInfos_.erase(resourceId);
        }

        HHBUI_LOG_DEBUG(L"资源已移除: " + std::wstring(resourceId.begin(), resourceId.end()));
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"资源移除时发生异常");
    }
}

bool UIResourceManager::HasResource(const std::string& resourceId) const {
    try {
        if (!initialized_.load()) {
            return false;
        }

        HHBUI_SHARED_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);
        return resources_.find(resourceId) != resources_.end();

    } catch (...) {
        return false;
    }
}

std::optional<ResourceInfo> UIResourceManager::GetResourceInfo(const std::string& resourceId) const {
    try {
        if (!initialized_.load()) {
            return std::nullopt;
        }

        HHBUI_SHARED_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);

        auto it = resourceInfos_.find(resourceId);
        if (it != resourceInfos_.end()) {
            return it->second;
        }

        return std::nullopt;

    } catch (...) {
        return std::nullopt;
    }
}

std::vector<std::string> UIResourceManager::GetAllResources() const {
    try {
        if (!initialized_.load()) {
            return {};
        }

        HHBUI_SHARED_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);

        std::vector<std::string> resourceIds;
        resourceIds.reserve(resources_.size());

        for (const auto& [id, resource] : resources_) {
            resourceIds.push_back(id);
        }

        return resourceIds;

    } catch (...) {
        return {};
    }
}

std::vector<std::string> UIResourceManager::GetResourcesByType(ResourceType type) const {
    try {
        if (!initialized_.load()) {
            return {};
        }

        HHBUI_SHARED_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);

        std::vector<std::string> resourceIds;

        for (const auto& [id, info] : resourceInfos_) {
            if (info.type == type) {
                resourceIds.push_back(id);
            }
        }

        return resourceIds;

    } catch (...) {
        return {};
    }
}

// ==================== 内存管理实现 ====================

void* UIResourceManager::AllocateMemory(size_t size) {
    try {
        if (!initialized_.load()) {
            return nullptr;
        }

        if (size == 0) {
            return nullptr;
        }

        std::lock_guard<std::mutex> lock(memoryMutex_);

        // 选择合适的内存池
        MemoryPool* pool = SelectMemoryPool(size);
        if (!pool) {
            // 如果没有合适的池，直接分配
            void* ptr = std::aligned_alloc(32, size);
            if (ptr) {
                allocations_[ptr] = nullptr; // 标记为直接分配
            }
            return ptr;
        }

        void* ptr = pool->Allocate(size);
        if (ptr) {
            allocations_[ptr] = pool;
        } else {
            // 池分配失败，检查内存压力
            if (CheckMemoryPressure()) {
                NotifyLowMemory(GetMemoryStatistics().totalFree, size);
            }
        }

        return ptr;

    } catch (...) {
        return nullptr;
    }
}

void UIResourceManager::DeallocateMemory(void* ptr) {
    try {
        if (!ptr || !initialized_.load()) {
            return;
        }

        std::lock_guard<std::mutex> lock(memoryMutex_);

        auto it = allocations_.find(ptr);
        if (it != allocations_.end()) {
            if (it->second) {
                // 池分配的内存
                it->second->Deallocate(ptr);
            } else {
                // 直接分配的内存
                std::free(ptr);
            }
            allocations_.erase(it);
        }

    } catch (...) {
        // 忽略释放异常
    }
}

MemoryStatistics UIResourceManager::GetMemoryStatistics() const {
    try {
        if (!initialized_.load()) {
            return MemoryStatistics();
        }

        std::lock_guard<std::mutex> lock(memoryMutex_);

        MemoryStatistics totalStats;

        for (const auto& pool : memoryPools_) {
            if (pool) {
                auto poolStats = pool->GetStatistics();
                totalStats.totalAllocated += poolStats.totalAllocated;
                totalStats.totalUsed += poolStats.totalUsed;
                totalStats.totalFree += poolStats.totalFree;
                totalStats.allocationCount += poolStats.allocationCount;
                totalStats.deallocationCount += poolStats.deallocationCount;

                // 合并池统计
                for (const auto& [type, usage] : poolStats.poolUsage) {
                    totalStats.poolUsage[type] += usage;
                }
                for (const auto& [type, count] : poolStats.poolCount) {
                    totalStats.poolCount[type] += count;
                }
            }
        }

        // 计算峰值使用量（简化实现）
        totalStats.peakUsage = totalStats.totalUsed;

        // 计算总体碎片化率
        if (totalStats.totalAllocated > 0) {
            totalStats.fragmentationRatio = (totalStats.totalAllocated - totalStats.totalUsed) * 100 / totalStats.totalAllocated;
        }

        return totalStats;

    } catch (...) {
        return MemoryStatistics();
    }
}

std::unordered_map<std::string, size_t> UIResourceManager::GetMemoryUsage() const {
    try {
        std::unordered_map<std::string, size_t> usage;

        auto stats = GetMemoryStatistics();
        usage["total_allocated"] = stats.totalAllocated;
        usage["total_used"] = stats.totalUsed;
        usage["total_free"] = stats.totalFree;
        usage["peak_usage"] = stats.peakUsage;
        usage["allocation_count"] = stats.allocationCount;
        usage["deallocation_count"] = stats.deallocationCount;
        usage["fragmentation_ratio"] = stats.fragmentationRatio;

        // 按池类型统计
        for (const auto& [type, poolUsage] : stats.poolUsage) {
            std::string poolName = "pool_" + std::to_string(static_cast<uint32_t>(type));
            usage[poolName + "_usage"] = poolUsage;
        }

        for (const auto& [type, poolCount] : stats.poolCount) {
            std::string poolName = "pool_" + std::to_string(static_cast<uint32_t>(type));
            usage[poolName + "_count"] = poolCount;
        }

        // 资源统计
        {
            HHBUI_SHARED_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);
            usage["resource_count"] = resources_.size();

            size_t totalResourceSize = 0;
            for (const auto& [id, info] : resourceInfos_) {
                totalResourceSize += info.size;
            }
            usage["resource_memory"] = totalResourceSize;
        }

        return usage;

    } catch (...) {
        return {};
    }
}

// ==================== 垃圾回收实现 ====================

VoidResult UIResourceManager::CollectGarbage(bool forceCollection) {
    try {
        if (!initialized_.load()) {
            return VoidResult(E_FAIL, L"资源管理器未初始化");
        }

        if (!gcEnabled_.load() && !forceCollection) {
            return VoidResult(E_FAIL, L"垃圾回收已禁用");
        }

        auto startTime = std::chrono::steady_clock::now();
        size_t collectedObjects = PerformGarbageCollection(forceCollection);
        auto endTime = std::chrono::steady_clock::now();

        // 更新统计
        {
            std::lock_guard<std::mutex> lock(gcMutex_);
            gcStats_.totalCollections++;
            gcStats_.objectsCollected += collectedObjects;
            gcStats_.totalTime += std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            gcStats_.lastCollection = endTime;
        }

        // 触发回调
        NotifyGC(gcStats_);

        HHBUI_LOG_DEBUG(L"垃圾回收完成，回收 " + std::to_wstring(collectedObjects) + L" 个对象");
        return VoidResult();

    } catch (...) {
        return VoidResult(E_UNEXPECTED, L"垃圾回收时发生异常");
    }
}

void UIResourceManager::StartAutoGC(std::chrono::milliseconds interval) {
    try {
        if (autoGCRunning_.load()) {
            return; // 已经在运行
        }

        autoGCRunning_.store(true);
        autoGCThread_ = std::thread(&UIResourceManager::AutoGCThread, this);

        HHBUI_LOG_DEBUG(L"自动垃圾回收已启动，间隔: " + std::to_wstring(interval.count()) + L"ms");

    } catch (...) {
        HHBUI_LOG_WARNING(L"启动自动垃圾回收时发生异常");
    }
}

void UIResourceManager::StopAutoGC() {
    try {
        if (!autoGCRunning_.load()) {
            return;
        }

        autoGCRunning_.store(false);
        gcCondition_.notify_all();

        if (autoGCThread_.joinable()) {
            autoGCThread_.join();
        }

        HHBUI_LOG_DEBUG(L"自动垃圾回收已停止");

    } catch (...) {
        HHBUI_LOG_WARNING(L"停止自动垃圾回收时发生异常");
    }
}

GCStatistics UIResourceManager::GetGCStatistics() const {
    try {
        std::lock_guard<std::mutex> lock(gcMutex_);
        return gcStats_;
    } catch (...) {
        return GCStatistics();
    }
}

// ==================== 私有方法实现 ====================

MemoryPool* UIResourceManager::SelectMemoryPool(size_t size) {
    // 根据大小选择合适的内存池
    if (size <= 1024) {
        return memoryPools_[static_cast<size_t>(MemoryPoolType::Small)].get();
    } else if (size <= 64 * 1024) {
        return memoryPools_[static_cast<size_t>(MemoryPoolType::Medium)].get();
    } else if (size <= 1024 * 1024) {
        return memoryPools_[static_cast<size_t>(MemoryPoolType::Large)].get();
    } else {
        return memoryPools_[static_cast<size_t>(MemoryPoolType::Huge)].get();
    }
}

size_t UIResourceManager::PerformGarbageCollection(bool forceCollection) {
    size_t collectedCount = 0;

    try {
        // 清理资源
        {
            HHBUI_UNIQUE_LOCK<HHBUI_SHARED_MUTEX> lock(resourceMutex_);

            auto it = resources_.begin();
            while (it != resources_.end()) {
                bool shouldCollect = forceCollection;

                if (!shouldCollect) {
                    // 检查引用计数
                    if (it->second && it->second->GetRefCount() <= 1) {
                        shouldCollect = true;
                    }

                    // 检查最后访问时间
                    auto infoIt = resourceInfos_.find(it->first);
                    if (infoIt != resourceInfos_.end()) {
                        auto now = std::chrono::steady_clock::now();
                        auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - infoIt->second.lastAccess);
                        if (elapsed.count() > 30) { // 30分钟未访问
                            shouldCollect = true;
                        }
                    }
                }

                if (shouldCollect) {
                    resourceInfos_.erase(it->first);
                    it = resources_.erase(it);
                    collectedCount++;
                } else {
                    ++it;
                }
            }
        }

        // 清理内存池
        {
            std::lock_guard<std::mutex> lock(memoryMutex_);
            for (auto& pool : memoryPools_) {
                if (pool) {
                    collectedCount += pool->Cleanup();
                }
            }
        }

    } catch (...) {
        // 忽略垃圾回收异常
    }

    return collectedCount;
}

bool UIResourceManager::CheckMemoryPressure() const {
    auto stats = GetMemoryStatistics();

    // 简单的内存压力检查
    if (stats.totalAllocated > 0) {
        double usageRatio = static_cast<double>(stats.totalUsed) / stats.totalAllocated;
        return usageRatio > 0.8; // 使用率超过80%
    }

    return false;
}

void UIResourceManager::AutoGCThread() {
    try {
        std::unique_lock<std::mutex> lock(gcMutex_);

        while (autoGCRunning_.load() && !shutdownRequested_.load()) {
            // 等待指定间隔
            std::chrono::milliseconds interval;
            {
                std::lock_guard<std::mutex> configLock(configMutex_);
                interval = config_.gcInterval;
            }

            if (gcCondition_.wait_for(lock, interval) == std::cv_status::timeout) {
                // 超时，执行垃圾回收
                lock.unlock();
                CollectGarbage(false);
                lock.lock();
            }
        }

    } catch (...) {
        HHBUI_LOG_WARNING(L"自动垃圾回收线程异常");
    }
}

void UIResourceManager::NotifyResourceLoad(const std::string& resourceId, bool success) {
    try {
        std::lock_guard<std::mutex> lock(callbackMutex_);
        for (const auto& [id, callback] : loadCallbacks_) {
            try {
                callback(resourceId, success);
            } catch (...) {
                // 忽略回调异常
            }
        }
    } catch (...) {
        // 忽略通知异常
    }
}

void UIResourceManager::NotifyLowMemory(size_t available, size_t requested) {
    try {
        std::lock_guard<std::mutex> lock(callbackMutex_);
        for (const auto& [id, callback] : lowMemoryCallbacks_) {
            try {
                callback(available, requested);
            } catch (...) {
                // 忽略回调异常
            }
        }
    } catch (...) {
        // 忽略通知异常
    }
}

void UIResourceManager::NotifyGC(const GCStatistics& stats) {
    try {
        std::lock_guard<std::mutex> lock(callbackMutex_);
        for (const auto& [id, callback] : gcCallbacks_) {
            try {
                callback(stats);
            } catch (...) {
                // 忽略回调异常
            }
        }
    } catch (...) {
        // 忽略通知异常
    }
}

uint64_t UIResourceManager::GetNextCallbackId() {
    return nextCallbackId_.fetch_add(1, std::memory_order_relaxed);
}

} // namespace HHBUI
