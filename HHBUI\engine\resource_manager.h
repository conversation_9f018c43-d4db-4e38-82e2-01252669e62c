/**
 * @file resource_manager.h
 * @brief 资源管理器 - 高性能资源池和内存管理系统
 * <AUTHOR> Team
 * @version 2.0.0
 * @date 2025-01-30
 */

#pragma once

#include <memory>
#include <unordered_map>
#include <vector>
#include <string>
#include <functional>
#include <mutex>
#include <atomic>
#include <chrono>
#include <thread>
#include <condition_variable>
#include <queue>

namespace HHBUI {

// ==================== 资源相关枚举和结构体 ====================

/// 资源类型
enum class ResourceType : uint32_t {
    Unknown = 0,
    Texture,
    Font,
    Brush,
    Bitmap,
    Geometry,
    Effect,
    Buffer,
    Shader,
    Custom
};

/// 资源状态
enum class ResourceState : uint32_t {
    Unloaded = 0,
    Loading,
    Loaded,
    Error,
    Disposing
};

/// 内存池类型
enum class MemoryPoolType : uint32_t {
    Small = 0,      // < 1KB
    Medium,         // 1KB - 64KB
    Large,          // 64KB - 1MB
    Huge            // > 1MB
};

/// 资源信息
struct ResourceInfo {
    std::string id;
    ResourceType type;
    ResourceState state;
    size_t size;
    std::chrono::steady_clock::time_point createTime;
    std::chrono::steady_clock::time_point lastAccess;
    uint32_t accessCount;
    uint32_t refCount;
    std::string source; // 文件路径或描述
    
    ResourceInfo() : type(ResourceType::Unknown), state(ResourceState::Unloaded), 
                    size(0), accessCount(0), refCount(0) {}
};

/// 内存块信息
struct MemoryBlock {
    void* ptr;
    size_t size;
    MemoryPoolType poolType;
    std::chrono::steady_clock::time_point allocTime;
    bool inUse;
    
    MemoryBlock() : ptr(nullptr), size(0), poolType(MemoryPoolType::Small), inUse(false) {}
    MemoryBlock(void* p, size_t s, MemoryPoolType type) 
        : ptr(p), size(s), poolType(type), allocTime(std::chrono::steady_clock::now()), inUse(true) {}
};

/// 内存统计信息
struct MemoryStatistics {
    size_t totalAllocated = 0;
    size_t totalUsed = 0;
    size_t totalFree = 0;
    size_t peakUsage = 0;
    size_t allocationCount = 0;
    size_t deallocationCount = 0;
    size_t fragmentationRatio = 0; // 百分比
    
    // 按池类型统计
    std::unordered_map<MemoryPoolType, size_t> poolUsage;
    std::unordered_map<MemoryPoolType, size_t> poolCount;
};

/// 垃圾回收统计
struct GCStatistics {
    size_t totalCollections = 0;
    size_t objectsCollected = 0;
    size_t memoryFreed = 0;
    std::chrono::milliseconds totalTime{0};
    std::chrono::steady_clock::time_point lastCollection;
    
    GCStatistics() : lastCollection(std::chrono::steady_clock::now()) {}
};

// ==================== 回调函数类型 ====================

/// 资源加载回调
using ResourceLoadCallback = std::function<void(const std::string& resourceId, bool success)>;

/// 内存不足回调
using LowMemoryCallback = std::function<void(size_t availableMemory, size_t requestedMemory)>;

/// 垃圾回收回调
using GCCallback = std::function<void(const GCStatistics& stats)>;

/// 资源清理回调
using ResourceCleanupCallback = std::function<void(const std::string& resourceId, ResourceType type)>;

// ==================== 资源基类 ====================

/**
 * @brief 资源基类 - 所有托管资源的基类
 */
class TOAPI IResource {
public:
    virtual ~IResource() = default;
    
    virtual ResourceType GetType() const = 0;
    virtual size_t GetSize() const = 0;
    virtual std::string GetId() const = 0;
    virtual bool IsValid() const = 0;
    virtual void Dispose() = 0;
    
    // 引用计数
    void AddRef() { refCount_.fetch_add(1, std::memory_order_relaxed); }
    void Release() { 
        if (refCount_.fetch_sub(1, std::memory_order_relaxed) == 1) {
            delete this;
        }
    }
    uint32_t GetRefCount() const { return refCount_.load(); }
    
protected:
    IResource() : refCount_(1) {}
    
private:
    std::atomic<uint32_t> refCount_;
};

/// 智能资源指针
template<typename T>
using ResourcePtr = std::shared_ptr<T>;

// ==================== 内存池 ====================

/**
 * @brief 内存池 - 高性能内存分配器
 */
class TOAPI MemoryPool {
public:
    MemoryPool(MemoryPoolType type, size_t blockSize, size_t initialBlocks = 16);
    ~MemoryPool();
    
    // 禁用拷贝和移动
    MemoryPool(const MemoryPool&) = delete;
    MemoryPool(MemoryPool&&) = delete;
    MemoryPool& operator=(const MemoryPool&) = delete;
    MemoryPool& operator=(MemoryPool&&) = delete;
    
    /// 分配内存
    void* Allocate(size_t size);
    
    /// 释放内存
    void Deallocate(void* ptr);
    
    /// 获取池类型
    MemoryPoolType GetType() const { return type_; }
    
    /// 获取块大小
    size_t GetBlockSize() const { return blockSize_; }
    
    /// 获取统计信息
    MemoryStatistics GetStatistics() const;
    
    /// 清理未使用的块
    size_t Cleanup();

private:
    MemoryPoolType type_;
    size_t blockSize_;
    std::vector<MemoryBlock> blocks_;
    std::queue<size_t> freeBlocks_;
    mutable std::mutex mutex_;
    
    std::atomic<size_t> totalAllocated_;
    std::atomic<size_t> totalUsed_;
    std::atomic<size_t> allocationCount_;
    std::atomic<size_t> deallocationCount_;
    
    void ExpandPool();
};

// ==================== UIResourceManager类 ====================

/**
 * @brief 资源管理器 - 提供高性能资源管理和内存管理
 * 
 * 特性：
 * - 智能资源缓存
 * - 内存池管理
 * - 自动垃圾回收
 * - 内存泄漏检测
 * - 资源使用统计
 * - 异步资源加载
 */
class TOAPI UIResourceManager {
public:
    UIResourceManager();
    ~UIResourceManager();
    
    // 禁用拷贝和移动
    UIResourceManager(const UIResourceManager&) = delete;
    UIResourceManager(UIResourceManager&&) = delete;
    UIResourceManager& operator=(const UIResourceManager&) = delete;
    UIResourceManager& operator=(UIResourceManager&&) = delete;
    
    // ==================== 初始化和配置 ====================
    
    /**
     * @brief 初始化资源管理器
     * @param config 性能配置
     * @return 初始化结果
     */
    VoidResult Initialize(const PerformanceConfig& config);
    
    /**
     * @brief 关闭资源管理器
     */
    void Shutdown();
    
    /**
     * @brief 更新配置
     * @param config 新配置
     * @return 更新结果
     */
    VoidResult UpdateConfig(const PerformanceConfig& config);
    
    // ==================== 资源管理 ====================
    
    /**
     * @brief 注册资源
     * @param resource 资源对象
     * @return 注册结果
     */
    VoidResult RegisterResource(std::shared_ptr<IResource> resource);
    
    /**
     * @brief 获取资源
     * @param resourceId 资源ID
     * @return 资源对象
     */
    std::shared_ptr<IResource> GetResource(const std::string& resourceId);
    
    /**
     * @brief 移除资源
     * @param resourceId 资源ID
     * @return 移除结果
     */
    VoidResult RemoveResource(const std::string& resourceId);
    
    /**
     * @brief 检查资源是否存在
     * @param resourceId 资源ID
     * @return 是否存在
     */
    bool HasResource(const std::string& resourceId) const;
    
    /**
     * @brief 获取资源信息
     * @param resourceId 资源ID
     * @return 资源信息
     */
    std::optional<ResourceInfo> GetResourceInfo(const std::string& resourceId) const;
    
    /**
     * @brief 获取所有资源列表
     * @return 资源ID列表
     */
    std::vector<std::string> GetAllResources() const;
    
    /**
     * @brief 按类型获取资源列表
     * @param type 资源类型
     * @return 资源ID列表
     */
    std::vector<std::string> GetResourcesByType(ResourceType type) const;
    
    // ==================== 内存管理 ====================
    
    /**
     * @brief 分配内存
     * @param size 内存大小
     * @return 内存指针
     */
    void* AllocateMemory(size_t size);
    
    /**
     * @brief 释放内存
     * @param ptr 内存指针
     */
    void DeallocateMemory(void* ptr);
    
    /**
     * @brief 获取内存统计信息
     * @return 内存统计
     */
    MemoryStatistics GetMemoryStatistics() const;
    
    /**
     * @brief 检查内存使用情况
     * @return 内存使用信息
     */
    std::unordered_map<std::string, size_t> GetMemoryUsage() const;
    
    // ==================== 垃圾回收 ====================
    
    /**
     * @brief 执行垃圾回收
     * @param forceCollection 是否强制回收
     * @return 回收结果
     */
    VoidResult CollectGarbage(bool forceCollection = false);
    
    /**
     * @brief 启动自动垃圾回收
     * @param interval 回收间隔
     */
    void StartAutoGC(std::chrono::milliseconds interval = std::chrono::minutes(5));
    
    /**
     * @brief 停止自动垃圾回收
     */
    void StopAutoGC();
    
    /**
     * @brief 获取垃圾回收统计
     * @return GC统计信息
     */
    GCStatistics GetGCStatistics() const;
    
    // ==================== 缓存管理 ====================
    
    /**
     * @brief 清理缓存
     * @param cacheType 缓存类型（空字符串表示全部）
     * @return 清理结果
     */
    VoidResult ClearCache(const std::string& cacheType = "");
    
    /**
     * @brief 设置缓存限制
     * @param maxCacheSize 最大缓存大小
     * @param maxCacheCount 最大缓存数量
     */
    void SetCacheLimits(size_t maxCacheSize, size_t maxCacheCount);
    
    /**
     * @brief 获取缓存统计信息
     * @return 缓存统计
     */
    std::unordered_map<std::string, size_t> GetCacheStats() const;
    
    // ==================== 内存泄漏检测 ====================
    
    /**
     * @brief 检测内存泄漏
     * @return 泄漏信息列表
     */
    std::vector<std::string> DetectMemoryLeaks() const;
    
    /**
     * @brief 启动内存泄漏监控
     * @param checkInterval 检查间隔
     */
    void StartLeakDetection(std::chrono::minutes checkInterval = std::chrono::minutes(10));
    
    /**
     * @brief 停止内存泄漏监控
     */
    void StopLeakDetection();
    
    // ==================== 回调管理 ====================
    
    /**
     * @brief 注册资源加载回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterLoadCallback(const ResourceLoadCallback& callback);
    
    /**
     * @brief 注册内存不足回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterLowMemoryCallback(const LowMemoryCallback& callback);
    
    /**
     * @brief 注册垃圾回收回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterGCCallback(const GCCallback& callback);
    
    /**
     * @brief 取消注册回调
     * @param callbackId 回调ID
     * @return 取消结果
     */
    bool UnregisterCallback(uint64_t callbackId);

private:
    // ==================== 私有方法 ====================
    
    /// 选择合适的内存池
    MemoryPool* SelectMemoryPool(size_t size);
    
    /// 执行垃圾回收实现
    size_t PerformGarbageCollection(bool forceCollection);
    
    /// 检查内存压力
    bool CheckMemoryPressure() const;
    
    /// 自动垃圾回收线程
    void AutoGCThread();
    
    /// 内存泄漏检测线程
    void LeakDetectionThread();
    
    /// 触发回调
    void NotifyResourceLoad(const std::string& resourceId, bool success);
    void NotifyLowMemory(size_t available, size_t requested);
    void NotifyGC(const GCStatistics& stats);
    
    /// 获取下一个回调ID
    uint64_t GetNextCallbackId();
    
    // ==================== 私有成员变量 ====================
    
    /// 配置
    PerformanceConfig config_;
    mutable std::mutex configMutex_;
    
    /// 资源管理
    std::unordered_map<std::string, std::shared_ptr<IResource>> resources_;
    std::unordered_map<std::string, ResourceInfo> resourceInfos_;
    mutable std::shared_mutex resourceMutex_;
    
    /// 内存池
    std::vector<std::unique_ptr<MemoryPool>> memoryPools_;
    std::unordered_map<void*, MemoryPool*> allocations_;
    mutable std::mutex memoryMutex_;
    
    /// 垃圾回收
    std::atomic<bool> gcEnabled_;
    std::atomic<bool> autoGCRunning_;
    std::thread autoGCThread_;
    std::condition_variable gcCondition_;
    std::mutex gcMutex_;
    GCStatistics gcStats_;
    
    /// 内存泄漏检测
    std::atomic<bool> leakDetectionEnabled_;
    std::atomic<bool> leakDetectionRunning_;
    std::thread leakDetectionThread_;
    std::condition_variable leakCondition_;
    std::mutex leakMutex_;
    
    /// 缓存限制
    std::atomic<size_t> maxCacheSize_;
    std::atomic<size_t> maxCacheCount_;
    
    /// 回调管理
    std::atomic<uint64_t> nextCallbackId_;
    std::unordered_map<uint64_t, ResourceLoadCallback> loadCallbacks_;
    std::unordered_map<uint64_t, LowMemoryCallback> lowMemoryCallbacks_;
    std::unordered_map<uint64_t, GCCallback> gcCallbacks_;
    mutable std::mutex callbackMutex_;
    
    /// 状态标志
    std::atomic<bool> initialized_;
    std::atomic<bool> shutdownRequested_;
};

} // namespace HHBUI
