﻿#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include <functional>
#include <atomic>
#include <mutex>
#include <future>
#include <chrono>
#include <optional>
#include <variant>
#include "dpi_manager.h"
#include "font_manager.h"

namespace HHBUI {

// ==================== 前向声明 ====================
class UIEngineConfig;
class UIEngineLogger;
class UIEngineProfiler;
class UIResourceManager;
class UIDPIManager;
class UIFontManager;
class UIRenderManager;
class UIThreadManager;
class UIPluginManager;

// ==================== 类型定义 ====================
using EngineHandle = std::shared_ptr<class UIEngine>;
using ConfigHandle = std::shared_ptr<UIEngineConfig>;
using LoggerHandle = std::shared_ptr<UIEngineLogger>;

// ==================== 枚举定义 ====================

/// 引擎状态
enum class EngineState : uint32_t {
    Uninitialized = 0,
    Initializing,
    Initialized,
    Running,
    Paused,
    Stopping,
    Stopped,
    Error
};

/// 日志级别
enum class LogLevel : uint32_t {
    Trace = 0,
    Debug,
    Info,
    Warning,
    Error,
    Critical
};

/// 渲染设备类型
enum class RenderDeviceType : uint32_t {
    Auto = 0,
    Hardware,
    Software,
    Reference
};

/// DPI感知模式
enum class DPIAwarenessMode : uint32_t {
    Unaware = 0,
    SystemAware,
    PerMonitorAware,
    PerMonitorAwareV2
};

// ==================== 配置结构体 ====================

/// 字体配置
struct FontConfig {
    std::wstring faceName = L"Segoe UI";
    int32_t size = 14;
    uint32_t style = 0; // FontStyle flags
    bool enableSubpixelRendering = true;
    bool enableAntialiasing = true;

    FontConfig() = default;
    FontConfig(const std::wstring& face, int32_t sz, uint32_t st = 0)
        : faceName(face), size(sz), style(st) {}
};

/// DPI配置
struct DPIConfig {
    float customScale = 0.0f; // 0 = auto detect
    DPIAwarenessMode awarenessMode = DPIAwarenessMode::PerMonitorAwareV2;
    bool enableDynamicDPI = true;
    float minScale = 0.5f;
    float maxScale = 4.0f;

    DPIConfig() = default;
};

/// 渲染配置
struct RenderConfig {
    RenderDeviceType deviceType = RenderDeviceType::Auto;
    int32_t deviceIndex = -1; // -1 = auto select
    bool enableVSync = true;
    bool enableMultisampling = true;
    uint32_t multisampleCount = 4;
    bool enableHardwareAcceleration = true;
    uint32_t maxTextureSize = 8192;

    RenderConfig() = default;
};

/// 性能配置
struct PerformanceConfig {
    uint32_t targetFPS = 60;
    bool enableFPSLimit = true;
    bool enablePerformanceMonitoring = true;
    uint32_t memoryPoolSize = 64 * 1024 * 1024; // 64MB
    uint32_t maxCachedObjects = 1000;
    std::chrono::milliseconds gcInterval{5000}; // 5 seconds

    PerformanceConfig() = default;
};

/// 调试配置
struct DebugConfig {
    bool enableDebugMode = false;
    LogLevel logLevel = LogLevel::Info;
    bool enableMemoryLeakDetection = true;
    bool enablePerformanceProfiling = false;
    bool enableValidationLayers = false;
    std::wstring logFilePath;

    DebugConfig() = default;
};

/// 线程配置
struct ThreadConfig {
    uint32_t workerThreadCount = 0; // 0 = auto detect
    bool enableAsyncInitialization = true;
    bool enableBackgroundGC = true;
    uint32_t maxConcurrentTasks = 16;

    ThreadConfig() = default;
};

/// 主引擎配置
struct EngineInitConfig {
    HINSTANCE hInstance = nullptr;

    // 子系统配置
    FontConfig fontConfig;
    DPIConfig dpiConfig;
    RenderConfig renderConfig;
    PerformanceConfig performanceConfig;
    DebugConfig debugConfig;
    ThreadConfig threadConfig;

    // 扩展配置
    std::unordered_map<std::string, std::variant<int32_t, float, bool, std::string, std::wstring>> customProperties;

    EngineInitConfig() = default;

    // 便捷设置方法
    EngineInitConfig& SetDebugMode(bool enable) { debugConfig.enableDebugMode = enable; return *this; }
    EngineInitConfig& SetLogLevel(LogLevel level) { debugConfig.logLevel = level; return *this; }
    EngineInitConfig& SetFont(const std::wstring& face, int32_t size, uint32_t style = 0) {
        fontConfig.faceName = face;
        fontConfig.size = size;
        fontConfig.style = style;
        return *this;
    }
    EngineInitConfig& SetDPIScale(float scale) { dpiConfig.customScale = scale; return *this; }
    EngineInitConfig& SetRenderDevice(RenderDeviceType type, int32_t index = -1) {
        renderConfig.deviceType = type;
        renderConfig.deviceIndex = index;
        return *this;
    }
};

// ==================== 回调函数类型 ====================

/// 引擎状态变化回调
using EngineStateCallback = std::function<void(EngineState oldState, EngineState newState)>;

/// 错误处理回调
using ErrorCallback = std::function<void(HRESULT errorCode, const std::wstring& message, const std::wstring& details)>;

/// 性能监控回调
using PerformanceCallback = std::function<void(const std::unordered_map<std::string, double>& metrics)>;

// ==================== 异常类 ====================

/// 引擎异常基类
class EngineException : public std::exception {
public:
    EngineException(HRESULT code, const std::wstring& message, const std::wstring& file = L"", int line = 0)
        : errorCode_(code), message_(message), file_(file), line_(line) {
        // 转换为多字节字符串用于std::exception
        int size = WideCharToMultiByte(CP_UTF8, 0, message.c_str(), -1, nullptr, 0, nullptr, nullptr);
        if (size > 0) {
            messageA_.resize(size - 1);
            WideCharToMultiByte(CP_UTF8, 0, message.c_str(), -1, &messageA_[0], size, nullptr, nullptr);
        }
    }

    const char* what() const noexcept override { return messageA_.c_str(); }
    HRESULT GetErrorCode() const noexcept { return errorCode_; }
    const std::wstring& GetMessage() const noexcept { return message_; }
    const std::wstring& GetFile() const noexcept { return file_; }
    int GetLine() const noexcept { return line_; }

private:
    HRESULT errorCode_;
    std::wstring message_;
    std::wstring file_;
    int line_;
    std::string messageA_;
};

/// 初始化异常
class InitializationException : public EngineException {
public:
    InitializationException(HRESULT code, const std::wstring& message, const std::wstring& file = L"", int line = 0)
        : EngineException(code, message, file, line) {}
};

/// 配置异常
class ConfigurationException : public EngineException {
public:
    ConfigurationException(HRESULT code, const std::wstring& message, const std::wstring& file = L"", int line = 0)
        : EngineException(code, message, file, line) {}
};

/// 资源异常
class ResourceException : public EngineException {
public:
    ResourceException(HRESULT code, const std::wstring& message, const std::wstring& file = L"", int line = 0)
        : EngineException(code, message, file, line) {}
};

// ==================== 结果类型 ====================

/// 操作结果模板
template<typename T>
class Result {
public:
    Result() : success_(false), errorCode_(E_FAIL) {}
    Result(const T& value) : success_(true), value_(value), errorCode_(S_OK) {}
    Result(HRESULT error, const std::wstring& message = L"")
        : success_(false), errorCode_(error), errorMessage_(message) {}

    bool IsSuccess() const noexcept { return success_; }
    bool IsFailure() const noexcept { return !success_; }

    const T& GetValue() const {
        if (!success_) throw EngineException(errorCode_, errorMessage_);
        return value_;
    }

    T GetValueOr(const T& defaultValue) const noexcept {
        return success_ ? value_ : defaultValue;
    }

    HRESULT GetErrorCode() const noexcept { return errorCode_; }
    const std::wstring& GetErrorMessage() const noexcept { return errorMessage_; }

    explicit operator bool() const noexcept { return success_; }

private:
    bool success_;
    T value_;
    HRESULT errorCode_;
    std::wstring errorMessage_;
};

// 特化void类型
template<>
class Result<void> {
public:
    Result() : success_(true), errorCode_(S_OK) {}
    Result(HRESULT error, const std::wstring& message = L"")
        : success_(false), errorCode_(error), errorMessage_(message) {}

    bool IsSuccess() const noexcept { return success_; }
    bool IsFailure() const noexcept { return !success_; }

    HRESULT GetErrorCode() const noexcept { return errorCode_; }
    const std::wstring& GetErrorMessage() const noexcept { return errorMessage_; }

    explicit operator bool() const noexcept { return success_; }

private:
    bool success_;
    HRESULT errorCode_;
    std::wstring errorMessage_;
};

using VoidResult = Result<void>;

// ==================== UIEngine主类 ====================

/**
 * @brief HHBUI引擎主类 - 使用现代C++17设计的高性能UI引擎
 *
 * 特性：
 * - 线程安全的单例模式
 * - RAII资源管理
 * - 异步初始化支持
 * - 完整的错误处理和恢复机制
 * - 高性能资源管理和缓存
 * - 动态DPI感知
 * - 插件系统支持
 * - 详细的性能监控和分析
 */
class TOAPI UIEngine final {
public:
    // ==================== 单例访问 ====================

    /**
     * @brief 获取引擎实例
     * @return 引擎实例的共享指针
     */
    static EngineHandle GetInstance();

    /**
     * @brief 检查引擎是否已创建
     * @return true如果实例已存在
     */
    static bool HasInstance() noexcept;

    // ==================== 生命周期管理 ====================

    /**
     * @brief 同步初始化引擎
     * @param config 初始化配置
     * @return 初始化结果
     */
    VoidResult Initialize(const EngineInitConfig& config = EngineInitConfig{});

    /**
     * @brief 异步初始化引擎
     * @param config 初始化配置
     * @return 异步初始化任务的future
     */
    std::future<VoidResult> InitializeAsync(const EngineInitConfig& config = EngineInitConfig{});

    /**
     * @brief 关闭引擎
     * @param forceShutdown 是否强制关闭（跳过清理步骤）
     * @return 关闭结果
     */
    VoidResult Shutdown(bool forceShutdown = false);

    /**
     * @brief 异步关闭引擎
     * @param forceShutdown 是否强制关闭
     * @return 异步关闭任务的future
     */
    std::future<VoidResult> ShutdownAsync(bool forceShutdown = false);

    /**
     * @brief 重启引擎
     * @param newConfig 新的配置（可选）
     * @return 重启结果
     */
    VoidResult Restart(const std::optional<EngineInitConfig>& newConfig = std::nullopt);

    // ==================== 状态查询 ====================

    /**
     * @brief 获取当前引擎状态
     * @return 引擎状态
     */
    EngineState GetState() const noexcept;

    /**
     * @brief 检查引擎是否已初始化
     * @return true如果已初始化
     */
    bool IsInitialized() const noexcept;

    /**
     * @brief 检查引擎是否正在运行
     * @return true如果正在运行
     */
    bool IsRunning() const noexcept;

    /**
     * @brief 检查是否为调试模式
     * @return true如果为调试模式
     */
    bool IsDebugMode() const noexcept;

    /**
     * @brief 获取引擎版本信息
     * @return 版本字符串
     */
    static std::wstring GetVersion() noexcept;

    /**
     * @brief 获取引擎版本号
     * @return 版本号
     */
    static uint64_t GetVersionNumber() noexcept;

    /**
     * @brief 获取引擎运行时间
     * @return 运行时间（秒）
     */
    double GetRunningTime() const noexcept;

    // ==================== 配置管理 ====================

    /**
     * @brief 获取当前配置
     * @return 配置对象的共享指针
     */
    ConfigHandle GetConfig() const noexcept;

    /**
     * @brief 更新配置
     * @param config 新配置
     * @param applyImmediately 是否立即应用
     * @return 更新结果
     */
    VoidResult UpdateConfig(const EngineInitConfig& config, bool applyImmediately = true);

    /**
     * @brief 重新加载配置
     * @param configPath 配置文件路径（可选）
     * @return 重新加载结果
     */
    VoidResult ReloadConfig(const std::optional<std::wstring>& configPath = std::nullopt);

    /**
     * @brief 保存当前配置到文件
     * @param configPath 配置文件路径
     * @return 保存结果
     */
    VoidResult SaveConfig(const std::wstring& configPath) const;

    // ==================== DPI和缩放管理 ====================

    /**
     * @brief 计算DPI缩放值
     * @param value 原始值
     * @return 缩放后的值
     */
    float CalculateScaledValue(float value) const noexcept;

    /**
     * @brief 获取当前DPI缩放系数
     * @return DPI缩放系数
     */
    float GetDPIScale() const noexcept;

    /**
     * @brief 获取指定监视器的DPI缩放系数
     * @param monitor 监视器句柄
     * @return DPI缩放系数
     */
    float GetDPIScale(HMONITOR monitor) const noexcept;

    /**
     * @brief 设置自定义DPI缩放
     * @param scale 缩放系数
     * @return 设置结果
     */
    VoidResult SetCustomDPIScale(float scale);

    /**
     * @brief 重置DPI缩放为自动检测
     * @return 重置结果
     */
    VoidResult ResetDPIScale();

    // ==================== 字体管理 ====================

    /**
     * @brief 获取默认字体
     * @return 字体对象指针
     */
    std::shared_ptr<UIFont> GetDefaultFont() const noexcept;

    /**
     * @brief 设置默认字体
     * @param fontConfig 字体配置
     * @return 设置结果
     */
    VoidResult SetDefaultFont(const FontConfig& fontConfig);

    /**
     * @brief 创建字体
     * @param fontConfig 字体配置
     * @return 字体对象
     */
    Result<std::shared_ptr<UIFont>> CreateFont(const FontConfig& fontConfig);

    /**
     * @brief 从内存加载字体
     * @param fontData 字体数据
     * @param dataSize 数据大小
     * @param fontFace 字体名称
     * @return 加载结果
     */
    VoidResult LoadFontFromMemory(const void* fontData, size_t dataSize, const std::wstring& fontFace);

    // ==================== 资源管理 ====================

    /**
     * @brief 获取资源管理器
     * @return 资源管理器指针
     */
    std::shared_ptr<UIResourceManager> GetResourceManager() const noexcept;

    /**
     * @brief 执行垃圾回收
     * @param forceCollection 是否强制回收
     * @return 回收结果
     */
    VoidResult CollectGarbage(bool forceCollection = false);

    /**
     * @brief 获取内存使用统计
     * @return 内存使用信息
     */
    std::unordered_map<std::string, size_t> GetMemoryUsage() const;

    /**
     * @brief 清理缓存
     * @param cacheType 缓存类型（空字符串表示全部）
     * @return 清理结果
     */
    VoidResult ClearCache(const std::string& cacheType = "");

    // ==================== 性能监控 ====================

    /**
     * @brief 获取性能分析器
     * @return 性能分析器指针
     */
    std::shared_ptr<UIEngineProfiler> GetProfiler() const noexcept;

    /**
     * @brief 获取当前FPS
     * @return FPS值
     */
    double GetCurrentFPS() const noexcept;

    /**
     * @brief 获取平均FPS
     * @param sampleCount 采样数量
     * @return 平均FPS
     */
    double GetAverageFPS(uint32_t sampleCount = 60) const noexcept;

    /**
     * @brief 获取性能指标
     * @return 性能指标映射
     */
    std::unordered_map<std::string, double> GetPerformanceMetrics() const;

    /**
     * @brief 开始性能分析
     * @param profileName 分析名称
     */
    void BeginProfile(const std::string& profileName);

    /**
     * @brief 结束性能分析
     * @param profileName 分析名称
     * @return 分析耗时（毫秒）
     */
    double EndProfile(const std::string& profileName);

    // ==================== 日志系统 ====================

    /**
     * @brief 获取日志记录器
     * @return 日志记录器指针
     */
    LoggerHandle GetLogger() const noexcept;

    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param message 日志消息
     * @param file 文件名（可选）
     * @param line 行号（可选）
     */
    void Log(LogLevel level, const std::wstring& message,
             const std::wstring& file = L"", int line = 0) const;

    /**
     * @brief 设置日志级别
     * @param level 日志级别
     */
    void SetLogLevel(LogLevel level);

    // ==================== 事件和回调 ====================

    /**
     * @brief 注册状态变化回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterStateCallback(const EngineStateCallback& callback);

    /**
     * @brief 注册错误处理回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterErrorCallback(const ErrorCallback& callback);

    /**
     * @brief 注册性能监控回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterPerformanceCallback(const PerformanceCallback& callback);

    /**
     * @brief 取消注册回调
     * @param callbackId 回调ID
     * @return 取消结果
     */
    bool UnregisterCallback(uint64_t callbackId);

    // ==================== 线程管理 ====================

    /**
     * @brief 获取线程管理器
     * @return 线程管理器指针
     */
    std::shared_ptr<UIThreadManager> GetThreadManager() const noexcept;

    /**
     * @brief 在主线程执行任务
     * @param task 任务函数
     * @return 任务future
     */
    template<typename F>
    auto ExecuteOnMainThread(F&& task) -> std::future<decltype(task())>;

    /**
     * @brief 在工作线程执行任务
     * @param task 任务函数
     * @return 任务future
     */
    template<typename F>
    auto ExecuteOnWorkerThread(F&& task) -> std::future<decltype(task())>;

    // ==================== 插件系统 ====================

    /**
     * @brief 获取插件管理器
     * @return 插件管理器指针
     */
    std::shared_ptr<UIPluginManager> GetPluginManager() const noexcept;

    /**
     * @brief 加载插件
     * @param pluginPath 插件路径
     * @return 加载结果
     */
    VoidResult LoadPlugin(const std::wstring& pluginPath);

    /**
     * @brief 卸载插件
     * @param pluginName 插件名称
     * @return 卸载结果
     */
    VoidResult UnloadPlugin(const std::wstring& pluginName);

    /**
     * @brief 获取已加载的插件列表
     * @return 插件名称列表
     */
    std::vector<std::wstring> GetLoadedPlugins() const;

    // ==================== 调试和诊断 ====================

    /**
     * @brief 生成诊断报告
     * @param includePerformanceData 是否包含性能数据
     * @return 诊断报告
     */
    std::wstring GenerateDiagnosticReport(bool includePerformanceData = true) const;

    /**
     * @brief 导出诊断数据
     * @param filePath 导出文件路径
     * @return 导出结果
     */
    VoidResult ExportDiagnosticData(const std::wstring& filePath) const;

    /**
     * @brief 验证引擎状态
     * @return 验证结果
     */
    VoidResult ValidateEngineState() const;

    /**
     * @brief 检测内存泄漏
     * @return 泄漏检测结果
     */
    std::vector<std::wstring> DetectMemoryLeaks() const;

    // ==================== 兼容性接口 ====================

    /**
     * @brief 兼容旧版本的初始化接口
     * @param info 旧版本初始化信息
     * @return 初始化结果
     * @deprecated 请使用新的Initialize方法
     */
    [[deprecated("Use Initialize(const EngineInitConfig&) instead")]]
    static HRESULT Init(struct info_Init* info = nullptr);

    /**
     * @brief 兼容旧版本的反初始化接口
     * @return 反初始化结果
     * @deprecated 请使用新的Shutdown方法
     */
    [[deprecated("Use Shutdown() instead")]]
    static HRESULT UnInit();

    /**
     * @brief 兼容旧版本的调试模式查询
     * @return 是否为调试模式
     * @deprecated 请使用IsDebugMode方法
     */
    [[deprecated("Use IsDebugMode() instead")]]
    static BOOL QueryDebug();

    /**
     * @brief 兼容旧版本的初始化状态查询
     * @return 是否已初始化
     * @deprecated 请使用IsInitialized方法
     */
    [[deprecated("Use IsInitialized() instead")]]
    static BOOL QueryInit();

    /**
     * @brief 兼容旧版本的缩放计算
     * @param n 原始值
     * @return 缩放后的值
     * @deprecated 请使用CalculateScaledValue方法
     */
    [[deprecated("Use CalculateScaledValue() instead")]]
    static FLOAT fScale(FLOAT n);

    /**
     * @brief 兼容旧版本的缩放系数获取
     * @return 缩放系数
     * @deprecated 请使用GetDPIScale方法
     */
    [[deprecated("Use GetDPIScale() instead")]]
    static FLOAT GetDefaultScale();

    /**
     * @brief 兼容旧版本的时间获取
     * @return 运行时间
     * @deprecated 请使用GetRunningTime方法
     */
    [[deprecated("Use GetRunningTime() instead")]]
    static FLOAT GetTime();

    /**
     * @brief 兼容旧版本的版本获取
     * @return 版本字符串
     * @deprecated 请使用GetVersion方法
     */
    [[deprecated("Use GetVersion() instead")]]
    static LPCWSTR GetVersion();

private:
    // ==================== 构造和析构 ====================

    UIEngine();
    ~UIEngine();

    // 禁用拷贝和移动
    UIEngine(const UIEngine&) = delete;
    UIEngine(UIEngine&&) = delete;
    UIEngine& operator=(const UIEngine&) = delete;
    UIEngine& operator=(UIEngine&&) = delete;

    // ==================== 私有方法 ====================

    /// 内部初始化实现
    VoidResult InitializeInternal(const EngineInitConfig& config);

    /// 内部关闭实现
    VoidResult ShutdownInternal(bool forceShutdown);

    /// 初始化子系统
    VoidResult InitializeSubsystems(const EngineInitConfig& config);

    /// 关闭子系统
    VoidResult ShutdownSubsystems(bool forceShutdown);

    /// 验证配置
    VoidResult ValidateConfig(const EngineInitConfig& config) const;

    /// 应用配置
    VoidResult ApplyConfig(const EngineInitConfig& config);

    /// 设置引擎状态
    void SetState(EngineState newState);

    /// 触发状态变化回调
    void NotifyStateChange(EngineState oldState, EngineState newState);

    /// 触发错误回调
    void NotifyError(HRESULT errorCode, const std::wstring& message, const std::wstring& details = L"");

    /// 触发性能监控回调
    void NotifyPerformanceUpdate(const std::unordered_map<std::string, double>& metrics);

    /// 初始化COM
    VoidResult InitializeCOM();

    /// 关闭COM
    void ShutdownCOM();

    /// 初始化DPI感知
    VoidResult InitializeDPIAwareness(const DPIConfig& config);

    /// 初始化渲染系统
    VoidResult InitializeRenderSystem(const RenderConfig& config);

    /// 初始化字体系统
    VoidResult InitializeFontSystem(const FontConfig& config);

    /// 初始化线程系统
    VoidResult InitializeThreadSystem(const ThreadConfig& config);

    /// 检查系统兼容性
    VoidResult CheckSystemCompatibility() const;

    /// 获取下一个回调ID
    uint64_t GetNextCallbackId();

    // ==================== 私有成员变量 ====================

    /// 单例实例
    static std::shared_ptr<UIEngine> instance_;
    static std::mutex instanceMutex_;

    /// 引擎状态
    mutable std::mutex stateMutex_;
    std::atomic<EngineState> state_;

    /// 配置管理
    ConfigHandle config_;
    mutable std::shared_mutex configMutex_;

    /// 子系统管理器
    std::shared_ptr<UIResourceManager> resourceManager_;
    std::shared_ptr<UIDPIManager> dpiManager_;
    std::shared_ptr<UIFontManager> fontManager_;
    std::shared_ptr<UIRenderManager> renderManager_;
    std::shared_ptr<UIThreadManager> threadManager_;
    std::shared_ptr<UIPluginManager> pluginManager_;

    /// 日志和性能监控
    LoggerHandle logger_;
    std::shared_ptr<UIEngineProfiler> profiler_;

    /// 时间管理
    std::chrono::steady_clock::time_point startTime_;
    std::chrono::steady_clock::time_point initTime_;

    /// 回调管理
    mutable std::shared_mutex callbackMutex_;
    std::atomic<uint64_t> nextCallbackId_;
    std::unordered_map<uint64_t, EngineStateCallback> stateCallbacks_;
    std::unordered_map<uint64_t, ErrorCallback> errorCallbacks_;
    std::unordered_map<uint64_t, PerformanceCallback> performanceCallbacks_;

    /// COM初始化状态
    std::atomic<bool> comInitialized_;

    /// 兼容性支持
    static std::weak_ptr<UIEngine> legacyInstance_;
};

// ==================== 模板方法实现 ====================

template<typename F>
auto UIEngine::ExecuteOnMainThread(F&& task) -> std::future<decltype(task())> {
    if (threadManager_) {
        return threadManager_->ExecuteOnMainThread(std::forward<F>(task));
    }

    // 如果线程管理器不可用，直接在当前线程执行
    using ReturnType = decltype(task());
    std::promise<ReturnType> promise;
    auto future = promise.get_future();

    try {
        if constexpr (std::is_void_v<ReturnType>) {
            task();
            promise.set_value();
        } else {
            promise.set_value(task());
        }
    } catch (...) {
        promise.set_exception(std::current_exception());
    }

    return future;
}

template<typename F>
auto UIEngine::ExecuteOnWorkerThread(F&& task) -> std::future<decltype(task())> {
    if (threadManager_) {
        return threadManager_->ExecuteOnWorkerThread(std::forward<F>(task));
    }

    // 如果线程管理器不可用，使用std::async
    return std::async(std::launch::async, std::forward<F>(task));
}

// ==================== 便捷宏定义 ====================

#define HHBUI_ENGINE() HHBUI::UIEngine::GetInstance()
#define HHBUI_LOG(level, message) HHBUI_ENGINE()->Log(level, message, __FILEW__, __LINE__)
#define HHBUI_LOG_TRACE(message) HHBUI_LOG(HHBUI::LogLevel::Trace, message)
#define HHBUI_LOG_DEBUG(message) HHBUI_LOG(HHBUI::LogLevel::Debug, message)
#define HHBUI_LOG_INFO(message) HHBUI_LOG(HHBUI::LogLevel::Info, message)
#define HHBUI_LOG_WARNING(message) HHBUI_LOG(HHBUI::LogLevel::Warning, message)
#define HHBUI_LOG_ERROR(message) HHBUI_LOG(HHBUI::LogLevel::Error, message)
#define HHBUI_LOG_CRITICAL(message) HHBUI_LOG(HHBUI::LogLevel::Critical, message)

#define HHBUI_PROFILE_BEGIN(name) HHBUI_ENGINE()->BeginProfile(name)
#define HHBUI_PROFILE_END(name) HHBUI_ENGINE()->EndProfile(name)

#define HHBUI_SCALE(value) HHBUI_ENGINE()->CalculateScaledValue(value)

} // namespace HHBUI
