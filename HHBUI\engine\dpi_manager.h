/**
 * @file dpi_manager.h
 * @brief DPI管理器 - 高级DPI感知和多显示器支持
 * <AUTHOR> Team
 * @version 2.0.0
 * @date 2025-01-30
 */

#pragma once

#include <memory>
#include <unordered_map>
#include <vector>
#include <functional>
#include <mutex>
#include <atomic>
#include <chrono>
#include <string>
#include <optional>

// 前向声明
namespace HHBUI {
    template<typename T> class Result;
    using VoidResult = Result<void>;

    struct DPIConfig {
        float customScale = 0.0f;
        int awarenessMode = 0;
        bool enableDynamicDPI = true;
        float minScale = 0.5f;
        float maxScale = 4.0f;
    };
}

namespace HHBUI {

// ==================== DPI相关结构体 ====================

/// DPI信息
struct DPIInfo {
    uint32_t dpiX = USER_DEFAULT_SCREEN_DPI;
    uint32_t dpiY = USER_DEFAULT_SCREEN_DPI;
    float scaleX = 1.0f;
    float scaleY = 1.0f;
    
    DPIInfo() = default;
    DPIInfo(uint32_t x, uint32_t y) : dpiX(x), dpiY(y) {
        scaleX = static_cast<float>(dpiX) / USER_DEFAULT_SCREEN_DPI;
        scaleY = static_cast<float>(dpiY) / USER_DEFAULT_SCREEN_DPI;
    }
    
    bool operator==(const DPIInfo& other) const noexcept {
        return dpiX == other.dpiX && dpiY == other.dpiY;
    }
    
    bool operator!=(const DPIInfo& other) const noexcept {
        return !(*this == other);
    }
};

/// 监视器信息
struct MonitorInfo {
    HMONITOR handle = nullptr;
    RECT workArea = {};
    RECT fullArea = {};
    DPIInfo dpiInfo;
    std::wstring deviceName;
    bool isPrimary = false;
    
    MonitorInfo() = default;
    MonitorInfo(HMONITOR h) : handle(h) {}
};

/// DPI变化事件参数
struct DPIChangeEventArgs {
    HWND window = nullptr;
    HMONITOR monitor = nullptr;
    DPIInfo oldDPI;
    DPIInfo newDPI;
    RECT suggestedRect = {};
    
    DPIChangeEventArgs() = default;
};

// ==================== 回调函数类型 ====================

/// DPI变化回调
using DPIChangeCallback = std::function<void(const DPIChangeEventArgs& args)>;

/// 监视器变化回调
using MonitorChangeCallback = std::function<void(const std::vector<MonitorInfo>& monitors)>;

// ==================== UIDPIManager类 ====================

/**
 * @brief DPI管理器 - 提供高级DPI感知和多显示器支持
 * 
 * 特性：
 * - 动态DPI检测和响应
 * - 多显示器DPI管理
 * - DPI缓存和优化
 * - 自动缩放计算
 * - 监视器配置变化检测
 */
class TOAPI UIDPIManager {
public:
    UIDPIManager();
    ~UIDPIManager();
    
    // 禁用拷贝和移动
    UIDPIManager(const UIDPIManager&) = delete;
    UIDPIManager(UIDPIManager&&) = delete;
    UIDPIManager& operator=(const UIDPIManager&) = delete;
    UIDPIManager& operator=(UIDPIManager&&) = delete;
    
    // ==================== 初始化和配置 ====================
    
    /**
     * @brief 初始化DPI管理器
     * @param config DPI配置
     * @return 初始化结果
     */
    VoidResult Initialize(const DPIConfig& config);
    
    /**
     * @brief 关闭DPI管理器
     */
    void Shutdown();
    
    /**
     * @brief 更新配置
     * @param config 新配置
     * @return 更新结果
     */
    VoidResult UpdateConfig(const DPIConfig& config);
    
    // ==================== DPI查询和计算 ====================
    
    /**
     * @brief 获取系统DPI信息
     * @return DPI信息
     */
    DPIInfo GetSystemDPI() const;
    
    /**
     * @brief 获取指定窗口的DPI信息
     * @param window 窗口句柄
     * @return DPI信息
     */
    DPIInfo GetWindowDPI(HWND window) const;
    
    /**
     * @brief 获取指定监视器的DPI信息
     * @param monitor 监视器句柄
     * @return DPI信息
     */
    DPIInfo GetMonitorDPI(HMONITOR monitor) const;
    
    /**
     * @brief 获取指定点的DPI信息
     * @param point 屏幕坐标点
     * @return DPI信息
     */
    DPIInfo GetPointDPI(const POINT& point) const;
    
    /**
     * @brief 计算缩放后的值
     * @param value 原始值
     * @param dpi DPI信息（可选，默认使用系统DPI）
     * @return 缩放后的值
     */
    float CalculateScaledValue(float value, const DPIInfo* dpi = nullptr) const;
    
    /**
     * @brief 计算缩放后的尺寸
     * @param size 原始尺寸
     * @param dpi DPI信息（可选，默认使用系统DPI）
     * @return 缩放后的尺寸
     */
    SIZE CalculateScaledSize(const SIZE& size, const DPIInfo* dpi = nullptr) const;
    
    /**
     * @brief 计算缩放后的矩形
     * @param rect 原始矩形
     * @param dpi DPI信息（可选，默认使用系统DPI）
     * @return 缩放后的矩形
     */
    RECT CalculateScaledRect(const RECT& rect, const DPIInfo* dpi = nullptr) const;
    
    // ==================== 监视器管理 ====================
    
    /**
     * @brief 获取所有监视器信息
     * @return 监视器信息列表
     */
    std::vector<MonitorInfo> GetAllMonitors() const;
    
    /**
     * @brief 获取主监视器信息
     * @return 主监视器信息
     */
    MonitorInfo GetPrimaryMonitor() const;
    
    /**
     * @brief 获取指定窗口所在的监视器
     * @param window 窗口句柄
     * @return 监视器信息
     */
    MonitorInfo GetWindowMonitor(HWND window) const;
    
    /**
     * @brief 获取指定点所在的监视器
     * @param point 屏幕坐标点
     * @return 监视器信息
     */
    MonitorInfo GetPointMonitor(const POINT& point) const;
    
    /**
     * @brief 刷新监视器信息
     */
    void RefreshMonitors();
    
    // ==================== DPI变化处理 ====================
    
    /**
     * @brief 处理窗口DPI变化
     * @param window 窗口句柄
     * @param newDPI 新DPI值
     * @param suggestedRect 建议的窗口矩形
     * @return 处理结果
     */
    VoidResult HandleDPIChange(HWND window, UINT newDPI, const RECT& suggestedRect);
    
    /**
     * @brief 注册DPI变化回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterDPIChangeCallback(const DPIChangeCallback& callback);
    
    /**
     * @brief 注册监视器变化回调
     * @param callback 回调函数
     * @return 回调ID
     */
    uint64_t RegisterMonitorChangeCallback(const MonitorChangeCallback& callback);
    
    /**
     * @brief 取消注册回调
     * @param callbackId 回调ID
     * @return 取消结果
     */
    bool UnregisterCallback(uint64_t callbackId);
    
    // ==================== 缓存管理 ====================
    
    /**
     * @brief 清理DPI缓存
     */
    void ClearCache();
    
    /**
     * @brief 获取缓存统计信息
     * @return 缓存统计
     */
    std::unordered_map<std::string, size_t> GetCacheStats() const;
    
    // ==================== 调试和诊断 ====================
    
    /**
     * @brief 生成DPI诊断报告
     * @return 诊断报告
     */
    std::wstring GenerateDiagnosticReport() const;
    
    /**
     * @brief 验证DPI设置
     * @return 验证结果
     */
    VoidResult ValidateDPISettings() const;

private:
    // ==================== 私有方法 ====================
    
    /// 初始化DPI感知
    VoidResult InitializeDPIAwareness();
    
    /// 枚举监视器
    void EnumerateMonitors();
    
    /// 监视器枚举回调
    static BOOL CALLBACK MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData);
    
    /// 获取监视器DPI（内部实现）
    DPIInfo GetMonitorDPIInternal(HMONITOR monitor) const;
    
    /// 量化DPI缩放值
    float QuantizeScale(float scale) const;
    
    /// 触发DPI变化回调
    void NotifyDPIChange(const DPIChangeEventArgs& args);
    
    /// 触发监视器变化回调
    void NotifyMonitorChange();
    
    /// 获取下一个回调ID
    uint64_t GetNextCallbackId();
    
    // ==================== 私有成员变量 ====================
    
    /// 配置
    DPIConfig config_;
    mutable std::mutex configMutex_;
    
    /// 监视器信息缓存
    mutable std::vector<MonitorInfo> monitors_;
    mutable std::mutex monitorsMutex_;
    mutable std::chrono::steady_clock::time_point lastMonitorUpdate_;
    
    /// DPI缓存
    mutable std::unordered_map<HMONITOR, DPIInfo> dpiCache_;
    mutable std::mutex dpiCacheMutex_;
    
    /// 回调管理
    std::atomic<uint64_t> nextCallbackId_;
    std::unordered_map<uint64_t, DPIChangeCallback> dpiChangeCallbacks_;
    std::unordered_map<uint64_t, MonitorChangeCallback> monitorChangeCallbacks_;
    mutable std::mutex callbackMutex_;
    
    /// 状态标志
    std::atomic<bool> initialized_;
    std::atomic<bool> dpiAwarenessSet_;
    
    /// 系统API函数指针
    typedef HRESULT(WINAPI* GetDpiForMonitorProc)(HMONITOR, int, UINT*, UINT*);
    typedef UINT(WINAPI* GetDpiForWindowProc)(HWND);
    typedef BOOL(WINAPI* SetProcessDpiAwarenessContextProc)(DPI_AWARENESS_CONTEXT);
    
    GetDpiForMonitorProc getDpiForMonitor_;
    GetDpiForWindowProc getDpiForWindow_;
    SetProcessDpiAwarenessContextProc setProcessDpiAwarenessContext_;
    
    HMODULE shcoreModule_;
    HMODULE user32Module_;
};

} // namespace HHBUI
